{"typescript.preferences.importModuleSpecifier": "relative", "editor.formatOnSave": true, "editor.detectIndentation": false, "editor.defaultFormatter": "biomejs.biome", "editor.indentSize": 2, "prettier.enable": false, "eslint.enable": false, "editor.insertSpaces": true, "biome.enabled": true, "editor.codeActionsOnSave": {"source.organizeImports.biome": "explicit"}, "eslint.workingDirectories": [{"mode": "auto"}], "editor.lineNumbers": "relative", "editor.smoothScrolling": true, "prettier.printWidth": 80, "cSpell.words": ["APEC", "archivable", "camelcase", "checkmark", "CONV", "copyfiles", "dind", "dtos", "EMEA", "enterlocal", "enterlocalpg", "entertest", "entertestpg", "eventbridge", "fastify", "flushall", "gotrue", "hubspot", "kanban", "linebreak", "luxon", "maneskin", "millis", "mpim", "mrkdwn", "<PERSON><PERSON><PERSON>", "oneofs", "pino", "platformup", "plpgsql", "proto", "protobuf", "protoc", "resetlocal", "resettest", "rtracer", "seeddb", "setex", "SLASQS", "subtickets", "supabase", "temporalio", "THENA", "thenaappsplatform", "thenaplatform", "timestamptz", "tldts", "toplevel", "typeorm", "typesense", "uids", "usergroups", "uuidv", "wfclientup", "wfworker<PERSON>", "toplevel", "seeddb", "mpim", "usergroups", "upserts", "upserted", "metatype", "syncpack", "upserting", "helpdesk", "platformteams", "ptcm", "bullmq", "DDMM", "oneofs", "pg_trgm", "tiptap", "presigner", "<PERSON><PERSON><PERSON><PERSON>", "venv", "u<PERSON><PERSON>", "pyproject", "svix", "pytest", "vitest", "dbconstants", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ENOSTID", "subteam", "subteams", "deepseek", "noopener", "<PERSON><PERSON><PERSON><PERSON>", "usergroup", "fanout", "dbname", "ingestor", "unarchived"], "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}}