{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.detectIndentation": false,
  "editor.defaultFormatter": "biomejs.biome",
  "editor.indentSize": 2,
  "prettier.enable": false,
  "eslint.enable": false,
  "editor.insertSpaces": true,
  "biome.enabled": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports.biome": "explicit"
  },
  "eslint.workingDirectories": [
    {
      "mode": "auto"
    }
  ],
  // "editor.lineNumbers": "interval",
  "editor.smoothScrolling": true,
  "prettier.printWidth": 80,
  "cSpell.words": [
    "APEC",
    "archivable",
    "camelcase",
    "checkmark",
    "CONV",
    "copyfiles",
    "dind",
    "dtos",
    "EMEA",
    "enterlocal",
    "enterlocalpg",
    "entertest",
    "entertestpg",
    "eventbridge",
    "fastify",
    "flushall",
    "gotrue",
    "hubspot",
    "kanban",
    "linebreak",
    "luxon",
    "maneskin",
    "millis",
    "mpim",
    "mrkdwn",
    "nestjs",
    "oneofs",
    "pino",
    "platformup",
    "plpgsql",
    "proto",
    "protobuf",
    "protoc",
    "resetlocal",
    "resettest",
    "rtracer",
    "seeddb",
    "setex",
    "SLASQS",
    "subtickets",
    "supabase",
    "temporalio",
    "THENA",
    "thenaappsplatform",
    "thenaplatform",
    "timestamptz",
    "tldts",
    "toplevel",
    "typeorm",
    "typesense",
    "uids",
    "usergroups",
    "uuidv",
    "wfclientup",
    "wfworkerup",
    "toplevel",
    "seeddb",
    "mpim",
    "usergroups",
    "upserts",
    "upserted",
    "metatype",
    "syncpack",
    "upserting",
    "helpdesk",
    "platformteams",
    "ptcm",
    "bullmq",
    "DDMM",
    "oneofs",
    "pg_trgm",
    "tiptap",
    "presigner",
    "fuzzystrmatch",
    "venv",
    "uvicorn",
    "pyproject",
    "svix",
    "pytest",
    "vitest",
    "dbconstants",
    "thenaslackapp",
    "ENOSTID",
    "subteam",
    "subteams",
    "deepseek",
    "noopener",
    "noreferrer",
    "usergroup",
    "fanout",
    "dbname",
    "ingestor",
    "bigserial",
    "unarchived",
    "fallover"
  ],
  "[typescript]": {
    "editor.defaultFormatter": "biomejs.biome"
},
"workbench.colorCustomizations": {
    "activityBar.activeBackground": "#1f6fd0",
    "activityBar.background": "#1f6fd0",
    "activityBar.foreground": "#e7e7e7",
    "activityBar.inactiveForeground": "#e7e7e799",
    "activityBarBadge.background": "#ee90bb",
    "activityBarBadge.foreground": "#15202b",
    "commandCenter.border": "#e7e7e799",
    "sash.hoverBorder": "#1f6fd0",
    "statusBar.background": "#1857a4",
    "statusBar.foreground": "#e7e7e7",
    "statusBarItem.hoverBackground": "#1f6fd0",
    "statusBarItem.remoteBackground": "#1857a4",
    "statusBarItem.remoteForeground": "#e7e7e7",
    "titleBar.activeBackground": "#1857a4",
    "titleBar.activeForeground": "#e7e7e7",
    "titleBar.inactiveBackground": "#1857a499",
    "titleBar.inactiveForeground": "#e7e7e799"
},
"peacock.color": "#1857a4"
}
