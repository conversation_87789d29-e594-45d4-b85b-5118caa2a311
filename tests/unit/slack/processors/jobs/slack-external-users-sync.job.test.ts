import { beforeEach, describe, expect, it, vi } from 'vitest';
import { Repository } from 'typeorm';
import { SlackExternalUsersSyncJob } from '../../../../../src/slack/processors/jobs/slack-external-users-sync.job';
import { Channels, CustomerContacts, Installations } from '../../../../../src/database/entities';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories/channels.repository';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { ThenaPlatformApiProvider } from '../../../../../src/external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils/logger';

describe('SlackExternalUsersSyncJob', () => {
  let job: SlackExternalUsersSyncJob;
  let mockLogger: any;
  let mockCustomerContactsRepository: any;
  let mockChannelsRepository: any;
  let mockSlackWebAPIService: any;
  let mockExternalService: any;

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    mockCustomerContactsRepository = {
      find: vi.fn().mockResolvedValue([]),
      findOne: vi.fn().mockResolvedValue(null),
      save: vi.fn().mockResolvedValue({}),
      upsert: vi.fn().mockResolvedValue({}),
    };

    mockChannelsRepository = {
      findAll: vi.fn().mockResolvedValue([]),
      findByCondition: vi.fn().mockResolvedValue(null),
      update: vi.fn().mockResolvedValue({}),
    };

    mockSlackWebAPIService = {
      getConversationMembers: vi.fn().mockResolvedValue({
        ok: true,
        members: ['U12345', 'U67890'],
        response_metadata: { next_cursor: '' },
      }),
      getUserInfo: vi.fn().mockResolvedValue({
        ok: true,
        user: {
          id: 'U12345',
          team_id: 'T54321',
          profile: {
            email: '<EMAIL>',
            real_name: 'Test User',
            display_name: 'testuser',
            image_original: 'https://example.com/image.jpg',
            image_512: 'https://example.com/image_512.jpg',
            image_192: 'https://example.com/image_192.jpg',
            image_72: 'https://example.com/image_72.jpg',
            image_48: 'https://example.com/image_48.jpg',
            image_32: 'https://example.com/image_32.jpg',
            image_24: 'https://example.com/image_24.jpg',
          },
        },
      }),
      getTeamInfo: vi.fn().mockResolvedValue({
        ok: true,
        team: {
          id: 'T54321',
          name: 'Test Team',
          email_domain: 'example.com',
          icon: {
            image_102: 'https://example.com/team_logo.jpg',
          },
        },
      }),
    };

    mockExternalService = {
      createAccounts: vi.fn().mockResolvedValue([
        {
          id: 'acc123',
          name: 'Test Team',
          primaryDomain: 'example.com',
          logo: 'https://example.com/team_logo.jpg',
        },
      ]),
      ingestCustomerContacts: vi.fn().mockResolvedValue([
        {
          id: 'contact123',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
        },
      ]),
      createCustomObjectRecord: vi.fn().mockResolvedValue({
        id: 'record123',
      }),
      deleteCustomObjectRecord: vi.fn().mockResolvedValue({}),
    };

    job = new SlackExternalUsersSyncJob(
      mockLogger,
      mockCustomerContactsRepository as unknown as Repository<CustomerContacts>,
      mockChannelsRepository as unknown as ChannelsRepository,
      mockSlackWebAPIService as unknown as SlackWebAPIService,
      mockExternalService as unknown as ThenaPlatformApiProvider
    );
  });

  describe('execute', () => {
    it('should return early if no shared channels are found', async () => {
      const mockInstallation = {
        id: 'inst123',
        teamId: 'T12345',
        botToken: 'xoxb-test-token',
        teamName: 'Test Team',
        organization: { id: 'org123' },
      } as Installations;

      mockChannelsRepository.findAll.mockResolvedValue([]);

      await job.execute(mockInstallation);

      expect(mockLogger.log).toHaveBeenCalledWith('No shared channels found for external user sync');
      expect(mockSlackWebAPIService.getConversationMembers).not.toHaveBeenCalled();
    });

    it('should validate that provided channels belong to the installation', async () => {
      const mockInstallation = {
        id: 'inst123',
        teamId: 'T12345',
        botToken: 'xoxb-test-token',
        teamName: 'Test Team',
        organization: { id: 'org123' },
      } as Installations;

      const mockChannels = [
        {
          id: 'chan123',
          channelId: 'C12345',
          name: 'test-channel',
          installation: { id: 'inst123' },
          isShared: true,
        },
        {
          id: 'chan456',
          channelId: 'C67890',
          name: 'another-channel',
          installation: { id: 'inst456' }, // Different installation
          isShared: true,
        },
      ] as Channels[];

      await expect(job.execute(mockInstallation, mockChannels)).rejects.toThrow(
        'Some channels do not belong to the installation: C67890'
      );
    });

    it('should handle errors when getting conversation members', async () => {
      const mockInstallation = {
        id: 'inst123',
        teamId: 'T12345',
        botToken: 'xoxb-test-token',
        teamName: 'Test Team',
        organization: { id: 'org123' },
      } as Installations;

      const mockChannels = [
        {
          id: 'chan123',
          channelId: 'C12345',
          name: 'test-channel',
          installation: { id: 'inst123' },
          isShared: true,
        },
      ] as Channels[];

      mockChannelsRepository.findAll.mockResolvedValue(mockChannels);
      mockSlackWebAPIService.getConversationMembers.mockResolvedValue({
        ok: false,
        error: 'channel_not_found',
      });

      await job.execute(mockInstallation);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to get members for channel C12345: channel_not_found'
      );
    });

    it('should handle errors when getting user info', async () => {
      const mockInstallation = {
        id: 'inst123',
        teamId: 'T12345',
        botToken: 'xoxb-test-token',
        teamName: 'Test Team',
        organization: { id: 'org123' },
      } as Installations;

      const mockChannels = [
        {
          id: 'chan123',
          channelId: 'C12345',
          name: 'test-channel',
          installation: { id: 'inst123' },
          isShared: true,
        },
      ] as Channels[];

      mockChannelsRepository.findAll.mockResolvedValue(mockChannels);
      mockSlackWebAPIService.getUserInfo.mockRejectedValue(new Error('user_not_found'));

      await job.execute(mockInstallation);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to get user info for U12345: user_not_found'
      );
    });
  });

  describe('getTeamInfo', () => {
    it('should fetch team info for all unique teams', async () => {
      const mockInstallation = {
        id: 'inst123',
        teamId: 'T12345',
        botToken: 'xoxb-test-token',
        teamName: 'Test Team',
        organization: { id: 'org123' },
      } as Installations;

      const teamIds = ['T54321', 'T67890'];

      mockSlackWebAPIService.getTeamInfo
        .mockResolvedValueOnce({
          ok: true,
          team: {
            id: 'T54321',
            name: 'Test Team 1',
            email_domain: 'example1.com',
            icon: {
              image_102: 'https://example.com/team1_logo.jpg',
            },
          },
        })
        .mockResolvedValueOnce({
          ok: true,
          team: {
            id: 'T67890',
            name: 'Test Team 2',
            email_domain: 'example2.com',
            icon: {
              image_102: 'https://example.com/team2_logo.jpg',
            },
          },
        });

      const result = await (job as any).getTeamInfo(mockInstallation, teamIds);

      expect(result.size).toBe(2);
      expect(result.get('T54321')).toEqual({
        name: 'Test Team 1',
        domain: 'example1.com',
        logo: 'https://example.com/team1_logo.jpg',
      });
      expect(result.get('T67890')).toEqual({
        name: 'Test Team 2',
        domain: 'example2.com',
        logo: 'https://example.com/team2_logo.jpg',
      });
    });

    it('should handle errors when fetching team info', async () => {
      const mockInstallation = {
        id: 'inst123',
        teamId: 'T12345',
        botToken: 'xoxb-test-token',
        teamName: 'Test Team',
        organization: { id: 'org123' },
      } as Installations;

      const teamIds = ['T54321'];

      mockSlackWebAPIService.getTeamInfo.mockResolvedValue({
        ok: false,
        error: 'team_not_found',
      });

      const result = await (job as any).getTeamInfo(mockInstallation, teamIds);

      expect(result.size).toBe(0);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to get team info for team T54321, error: team_not_found'
      );
    });
  });

  describe('createPlatformAccounts', () => {
    it('should create platform accounts for teams', async () => {
      const mockInstallation = {
        id: 'inst123',
        teamId: 'T12345',
        botToken: 'xoxb-test-token',
        teamName: 'Test Team',
        organization: { id: 'org123' },
      } as Installations;

      const teamDomainMap = {
        'T54321': 'example.com',
      };

      const teamInfoMap = new Map();
      teamInfoMap.set('T54321', {
        name: 'Test Team',
        domain: 'example.com',
        logo: 'https://example.com/team_logo.jpg',
      });

      mockExternalService.createAccounts.mockResolvedValue([
        {
          id: 'acc123',
          name: 'Test Team',
          primaryDomain: 'example.com',
          logo: 'https://example.com/team_logo.jpg',
        },
      ]);

      const result = await (job as any).createPlatformAccounts(
        mockInstallation,
        teamDomainMap,
        teamInfoMap
      );

      expect(mockExternalService.createAccounts).toHaveBeenCalledWith(
        mockInstallation,
        [
          {
            name: 'Test Team',
            primaryDomain: 'example.com',
            logo: 'https://example.com/team_logo.jpg',
            source: 'Slack',
          },
        ]
      );

      expect(result).toEqual({
        'example.com': {
          id: 'acc123',
          name: 'Test Team',
          primaryDomain: 'example.com',
          logo: 'https://example.com/team_logo.jpg',
        },
      });
    });

    it('should handle missing team info', async () => {
      const mockInstallation = {
        id: 'inst123',
        teamId: 'T12345',
        botToken: 'xoxb-test-token',
        teamName: 'Test Team',
        organization: { id: 'org123' },
      } as Installations;

      const teamDomainMap = {
        'T54321': 'example.com',
      };

      const teamInfoMap = new Map();

      const result = await (job as any).createPlatformAccounts(
        mockInstallation,
        teamDomainMap,
        teamInfoMap
      );

      expect(mockLogger.error).toHaveBeenCalledWith('Team info not found for team T54321');
      expect(mockExternalService.createAccounts).toHaveBeenCalledWith(mockInstallation, []);
    });
  });

  describe('getEmail and getDomainFromEmail', () => {
    it('should get email from user profile', async () => {
      const user = {
        id: 'U12345',
        profile: {
          email: '<EMAIL>',
        },
      };

      const email = await (job as any).getEmail(user);
      expect(email).toBe('<EMAIL>');
    });

    it('should generate fallback email when profile email is missing', async () => {
      const user = {
        id: 'U12345',
        profile: {
          real_name: 'Test User',
        },
        team_id: 'T54321',
      };

      const email = await (job as any).getEmail(user);
      expect(email).toBe('<EMAIL>');
    });

    it('should extract domain from email', async () => {
      const user = {
        id: 'U12345',
        profile: {
          email: '<EMAIL>',
        },
      };

      const domain = await (job as any).getDomainFromEmail(user);
      expect(domain).toBe('example.com');
    });

    it('should return null when email is missing', async () => {
      const user = {
        id: 'U12345',
        profile: {
        },
      };

      const domain = await (job as any).getDomainFromEmail(user);
      expect(domain).toBeNull();
    });
  });
});
