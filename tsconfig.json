{
  "compilerOptions": {
    "outDir": "./dist",

    // "lib": ["ES2021.String"],
    // "baseUrl": "./",

    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowJs": true,
    "strict": true,
    "module": "node16",
    "moduleResolution": "node16",
    "declaration": true,
    "removeComments": true,
    "allowSyntheticDefaultImports": true,
    "target": "es2017",
    "sourceMap": true,
    "incremental": true,
    "skipLibCheck": true,
    "strictNullChecks": false,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "forceConsistentCasingInFileNames": false,
    "noFallthroughCasesInSwitch": false,
    "strictPropertyInitialization": false,
    "esModuleInterop": true,
    "inlineSources": true

    // Set `sourceRoot` to  "/" to strip the build path prefix
    // from generated source code references.
    // This improves issue grouping in Sentry.
    // Set `sourceRoot` to  "/" to strip the build path prefix
    // from generated source code references.
    // This improves issue grouping in Sentry.
    // "sourceRoot": "/"
  },
  "include": ["src/**/*", "./custom.d.ts"],
  "exclude": ["./global-setup.ts", "./global-teardown.ts", "dist"]
}
