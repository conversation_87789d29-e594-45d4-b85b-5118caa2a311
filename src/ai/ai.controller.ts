import {
  Controller,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  UseGuards,
} from '@nestjs/common';
import { GetBotCtx } from '../auth/decorators';
import { AuthGuard } from '../auth/guards';
import { BotCtx } from '../auth/interfaces';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../utils';
import { AiService } from './ai.service';

@Controller('v1/ai')
@UseGuards(AuthGuard)
export class AiController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Services
    private readonly aiService: AiService,
  ) {}

  @Get('/providers')
  async getAllProviders(@GetBotCtx() _botCtx: BotCtx) {
    try {
      const providers = await this.aiService.getProviders();
      return {
        data: providers,
        ok: true,
        count: providers.length,
        activeProvider: providers.find((p) => p.isActive)?.id || null,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Error) {
        this.logger.error(
          `Error fetching AI providers: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(`Error fetching AI providers: ${error}`);
      }

      throw new InternalServerErrorException(
        'An error occurred while fetching the AI providers',
      );
    }
  }

  @Get('/models')
  @Get('/models/:provider')
  async getModels(
    @GetBotCtx() _botCtx: BotCtx,
    @Param('provider') provider?: string,
  ) {
    try {
      // If no provider or "all" is specified, get models from all providers
      if (!provider || provider === 'all') {
        const providers = await this.aiService.getProviders();
        const allModels = [];

        // For each provider, get its models
        for (const providerInfo of providers) {
          const providerModels = await this.aiService.getModels(
            providerInfo.id,
          );
          // Add provider information to each model
          allModels.push(
            ...providerModels.map((model) => ({
              ...model,
              provider: providerInfo.id,
              providerName: providerInfo.name,
            })),
          );
        }

        return {
          data: allModels,
          ok: true,
          count: allModels.length,
          provider: 'all',
          activeModel: allModels.find((m) => m.isActive)?.id || null,
        };
      }

      // Otherwise get models for the specified provider
      const models = await this.aiService.getModels(provider);
      return {
        data: models,
        ok: true,
        count: models.length,
        provider,
        activeModel: models.find((m) => m.isActive)?.id || null,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Error) {
        this.logger.error(
          `Error fetching AI models: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(`Error fetching AI models: ${error}`);
      }

      throw new InternalServerErrorException(
        'An error occurred while fetching the AI models',
      );
    }
  }

  @Get('health')
  async checkAIHealth() {
    try {
      // Store the current provider
      const currentProvider = this.aiService.getActiveProvider();

      // Switch to OpenAI provider for this request
      this.aiService.setActiveProvider('openai');

      const startTime = Date.now();

      // Test the API with a simple prompt
      const result = await this.aiService.executePrompt({
        prompt: 'Is this working?',
        conversation: 'Just a test message',
        temperature: 0.1,
        maxTokens: 10,
      });

      const responseTime = Date.now() - startTime;

      // Switch back to the original provider
      this.aiService.setActiveProvider(currentProvider);

      return {
        status: result.success ? 'ok' : 'error',
        provider: 'openai',
        responseTime: `${responseTime}ms`,
        error: result.success ? null : result.error,
      };
    } catch (error) {
      return {
        status: 'error',
        provider: 'openai',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  @Get('health/openai')
  async checkOpenAIHealth() {
    return this.checkAIHealth();
  }
}
