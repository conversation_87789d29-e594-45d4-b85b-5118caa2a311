import dotenv from 'dotenv';

dotenv.config({ path: '.env' });

import './instrument';

import {
  HttpStatus,
  UnprocessableEntityException,
  ValidationPipe,
} from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { SlackService } from './slack/services/slack.service';
import { ILogger } from './utils';
import { HttpExceptionFilter } from './utils/filters';
import { ConfigKeys, ConfigService } from './config/config.service';
import { setupSwagger } from './utils/swagger';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    snapshot: true,
  });

  const logger = app.get<ILogger>('CustomLogger');
  // Pull the slack app from the nest module
  const slack = app.get(SlackService);

  // Use the slack app as the express receiver, this mounts the slack
  // routes at their default paths
  app.use('/', slack.use());

  // Enable cors
  app.enableCors({
    origin: '*',
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'x-api-key',
      'x-csrf-token',
      'x-requested-with',
      'x-forwarded-for',
      'x-forwarded-host',
      'x-auth-token',
      'x-slack-id',
      '*',
    ],
  });

  const configService = app.get(ConfigService);

  if (configService.getBoolean(ConfigKeys.GENERATE_SWAGGER)) {
    setupSwagger(app, configService);
  }


  // Add validation pipe to the app
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      exceptionFactory: (errors) => {
        const processErrors = (error: any): any[] => {
          const result = [];
          if (error.constraints) {
            result.push({
              property: error.property,
              value: error.value,
              constraints: error.constraints,
            });
          }

          if (error.children && error.children.length > 0) {
            for (const child of error.children) {
              result.push(
                ...processErrors(child).map((e) => ({
                  ...e,
                  property: `${error.property}.${e.property}`,
                })),
              );
            }
          }

          return result;
        };

        const messages = errors.flatMap(processErrors);

        return new UnprocessableEntityException({
          message: messages,
          error: 'Validation Error',
          statusCode: 422,
        });
      },
    }),
  );

  // Add the catch all filter to the app
  app.useGlobalFilters(new HttpExceptionFilter());

  const port = process.env.PORT ?? 3001;
  await app.listen(port);

  logger.log(`Server listening at http://localhost:${port}`);

  // Setup event handlers for errors
  const events = [
    'beforeExit',
    'uncaughtException',
    'unhandledRejection',
    'rejectionHandled',
    'exit',
  ];

  for (const event of events) {
    process.on(event, (error) => {
      logger.error(
        `🚨🚨🚨 Mayday mayday! Received a ${event} signal! Initiating graceful shutdown`,
      );
      if (error instanceof Error) {
        logger.error(
          `Shutting down due to Error message: ${error.message}, Error stack: ${error.stack}`,
        );
      }
    });
  }

  // Setup signal handlers for graceful shutdown
  const signals = ['SIGTERM', 'SIGINT'];

  for (const signal of signals) {
    process.on(signal, async () => {
      logger.log(
        `🚨🚨🚨 Mayday mayday! Received ${signal}. Starting graceful shutdown...`,
      );

      try {
        await app.close();
        logger.log('Application gracefully closed.');
        process.exit(0);
      } catch (error) {
        if (error instanceof Error) {
          logger.error(
            `Error during graceful shutdown: ${error.message}, Error stack: ${error.stack}`,
          );
        }

        process.exit(1);
      }
    });
  }
}

bootstrap();
