import dotenv from 'dotenv';

dotenv.config({ path: '.env' });

import './instrument';

import {
  HttpStatus,
  UnprocessableEntityException,
  ValidationPipe,
} from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import cors from 'cors';
import { AppModule } from './app.module';
import { SlackService } from './slack/services/slack.service';
import { HttpExceptionFilter } from './utils/filters';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    snapshot: true,
  });

  // Pull the slack app from the nest module
  const slack = app.get(SlackService);

  // Use the slack app as the express receiver, this mounts the slack
  // routes at their default paths
  app.use('/', slack.use());

  // Enable cors
  app.use(cors());

  // Add validation pipe to the app
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      exceptionFactory: (errors) => {
        const processErrors = (error: any): any[] => {
          const result = [];
          if (error.constraints) {
            result.push({
              property: error.property,
              value: error.value,
              constraints: error.constraints,
            });
          }

          if (error.children && error.children.length > 0) {
            for (const child of error.children) {
              result.push(
                ...processErrors(child).map((e) => ({
                  ...e,
                  property: `${error.property}.${e.property}`,
                })),
              );
            }
          }

          return result;
        };

        const messages = errors.flatMap(processErrors);

        return new UnprocessableEntityException({
          message: messages,
          error: 'Validation Error',
          statusCode: 422,
        });
      },
    }),
  );

  // Add the catch all filter to the app
  app.useGlobalFilters(new HttpExceptionFilter());

  await app.listen(process.env.PORT ?? 3002);
}

bootstrap();
