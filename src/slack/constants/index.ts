export const SLACK_SCOPES = [
  // Conversations
  'channels:history',
  'channels:manage',
  'channels:join',
  'channels:read',

  // Chats
  'chat:write',
  'chat:write.customize',

  // Commands
  'commands',

  // Files
  'files:read',
  'files:write',

  // Groups / Private Conversations
  'groups:history',
  'groups:read',
  'groups:write',
  'im:history',

  // Custom Emojis
  'emoji:read',

  // MPIMs
  'mpim:read',
  'mpim:write',

  // Reactions
  'reactions:read',
  'reactions:write',

  // Team
  'team:read',

  // Users
  'users:read.email',
  'users:read',

  // User Groups
  'usergroups:read',
  'usergroups:write',

  // Links
  'links:read',
  'links:write',
];

export const SLACK_SQS_PRODUCER_SERVICE_TOKEN = Symbol(
  'SlackSQSProducerService',
);

export const SlackEvents = [
  // Message events
  'message',

  // Members events
  'member_joined_channel',
  'member_left_channel',

  // Channels events
  'channel_created',
  'channel_deleted',
  'channel_rename',
  'channel_archive',
  'channel_unarchive',
  'channel_shared',
  'channel_unshared',
  'channel_left',

  // Subteams events
  'subteam_created',
  'subteam_updated',

  // Reactions events
  'reaction_added',
  'reaction_removed',

  // Links
  'link_shared',
];
