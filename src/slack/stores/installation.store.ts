import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Installation,
  InstallationQuery,
  InstallationStore,
} from '@slack/bolt';
import { Repository } from 'typeorm';
import { Installations } from '../../database/entities';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils/logger';

@Injectable()
export class TypeORMInstallationStore implements InstallationStore {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    @InjectRepository(Installations)
    private readonly installationsRepository: Repository<Installations>,
  ) {}

  /**
   * Store the installation in the database
   * @param installation The installation to store
   */
  async storeInstallation(_installation: Installation) {
    return;
  }

  /**
   * Fetch the installation from the database
   * @param query The query to fetch the installation
   * @returns The installation
   */
  async fetchInstallation(query: InstallationQuery<boolean>) {
    try {
      // Fetch the installation from the database
      const installation = await this.installationsRepository.findOne({
        where: { teamId: query.teamId },
        select: ['installationDump'],
      });

      // If the installation is not found, throw an error
      if (!installation) {
        throw new Error('No installation found');
      }

      // Return the installation
      return installation.installationDump;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error fetching installation: ${error.message}`,
          error.stack,
        );
      } else {
        console.error('Failed to fetch installation', error);
      }

      return;
    }
  }

  /**
   * Delete the installation from the database
   * @param query The query to delete the installation
   */
  async deleteInstallation(_query: InstallationQuery<boolean>) {
    return;
  }
}
