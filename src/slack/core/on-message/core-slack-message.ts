import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { KnownEventFromType } from '@slack/bolt';
import { In, IsNull, Repository } from 'typeorm';
import { AiService } from '../../../ai/ai.service';
import {
  CustomerContacts,
  Installations,
  PlatformTeams,
  Users,
} from '../../../database/entities';
import {
  ChannelType,
  Channels,
} from '../../../database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../database/entities/channels/repositories';
import { TeamRelationshipType } from '../../../database/entities/mappings/platform-teams-to-channel-mappings.entity';
import { SubGroupsMapsRepository } from '../../../database/entities/mappings/repositories/sub-groups-maps.repository';
import { GroupedSlackMessagesRepository } from '../../../database/entities/slack-messages/repositories/grouped-slack-messages.repository';
import { TeamsRepository } from '../../../database/entities/teams';
import { Person } from '../../../database/interfaces/person.interface';
import { CreateNewComment } from '../../../external/provider/interfaces';
import { CreateNewTicket } from '../../../external/provider/interfaces/tickets.interface';
import { ThenaPlatformApiProvider } from '../../../external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils';
import { getSlackMentions } from '../../../utils/common';
import { SlackWebAPIService } from '../../providers/slack-apis/slack-apis.service';
import { ConversationGroupingService } from '../conversation-grouping';
import { SettingsCore, SlackAppManagementService } from '../management';

@Injectable()
export class CoreSlackMessage {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Database Repositories
    @InjectRepository(Users)
    private readonly usersRepository: Repository<Users>,
    @InjectRepository(CustomerContacts)
    private readonly customerContactsRepository: Repository<CustomerContacts>,
    private readonly channelsRepository: ChannelsRepository,
    private readonly groupedSlackMessagesRepository: GroupedSlackMessagesRepository,
    private readonly subGroupsMapsRepository: SubGroupsMapsRepository,
    private readonly teamsRepository: TeamsRepository,

    // Injected Services
    private readonly conversationGroupingService: ConversationGroupingService,
    private readonly settingsCore: SettingsCore,

    // External API Providers
    private readonly slackAppManagementService: SlackAppManagementService,
    private readonly platformApiProvider: ThenaPlatformApiProvider,
    private readonly slackWebAPIService: SlackWebAPIService,

    // AI Providers
    private readonly aiService: AiService,
  ) {}

  /**
   * Check and get the slack message details
   * @param installation Installation
   * @param event Message event
   * @returns Slack message details
   */
  async checkAndGetSlackMessageDetails(
    installation: Installations,
    event: KnownEventFromType<'message'>,
  ) {
    // TypeGuard: Validate that the message has text to fetch the union type [[Possibly] A Top Level Message]
    const topLevelMessage = 'text' in event;
    if (!topLevelMessage) {
      throw new Error('Message is not a top level message');
    }

    // TypeGuard: Validate that the message has a team
    if (!('team' in event)) {
      throw new Error('Slack workspace not attached to the event message!');
    }

    // If the message is in a direct message channel, we don't need to create a ticket
    if (event.channel.startsWith('D')) {
      this.logger.debug(
        `Message received in a direct message channel ${event.channel}! Skipping ticket creation.`,
      );

      return;
    }

    // Get the channel from the database
    const channel = await this.getChannel(installation, event);
    if (!channel) {
      this.logger.debug(
        `Channel ${event.channel} not found in the database! Skipping ticket creation.`,
      );

      return { channel, user: null, isCustomer: false, isGrouped: false };
    }

    // Get the user from the database
    const { user, isCustomer } = await this.getUser(
      installation,
      event,
      channel,
    );

    // If the user is not a customer and the channel is not an internal helpdesk channel, we don't need to create a ticket
    if (!isCustomer && channel.channelType !== ChannelType.INTERNAL_HELPDESK) {
      this.logger.debug(
        `User ${user.slackId} is not a customer! Skipping ticket creation.`,
      );

      return { channel, user, isCustomer, isGrouped: false };
    }

    // Check if we should group this message with an existing conversation
    const { isGrouped } = await this.shouldGroupWithExistingConversation(
      installation,
      channel,
      user,
      event,
    );

    return { channel, user, isCustomer, isGrouped };
  }

  /**
   * Get the ticket details using AI
   * @param installation Installation
   * @param platformTeam Platform team
   * @param event Message event
   * @returns Ticket details
   */
  async aiGetTicketDetails(
    installation: Installations,
    platformTeam: PlatformTeams,
    event: KnownEventFromType<'message'>,
  ) {
    if (!('text' in event)) {
      throw new Error('Message is not a top level message');
    }

    this.logger.log('Getting priorities for the team');

    // Get the priorities for the team
    const getPrioritiesResponse =
      await this.platformApiProvider.getPrioritiesForTeam(
        installation,
        platformTeam.uid,
      );

    // Prepare available priorities for AI processing
    let availablePriorities: { name: string; description: string }[] = [];
    if (getPrioritiesResponse.length > 0) {
      // Get the available priorities
      availablePriorities = getPrioritiesResponse.map((p) => ({
        name: p.name,
        description: p?.description,
      }));
    } else {
      // Use default priorities if none available
      availablePriorities = [
        { name: 'Low', description: 'Low priority ticket' },
        { name: 'Medium', description: 'Medium priority ticket' },
        { name: 'High', description: 'High priority ticket' },
        { name: 'Urgent', description: 'Urgent priority ticket' },
      ];
    }

    this.logger.log('Detecting urgency and sentiment');

    // Set the active model
    const aiModel = await this.settingsCore.getValue('ai_model', {
      platformTeam,
      workspace: installation,
    });

    this.setAiProviderAndModel(aiModel);

    // Load the team prompts
    await this.aiService.loadTeamPrompts(
      platformTeam.id,
      installation.id,
      installation.organization.id,
    );

    // Detect urgency and sentiment
    const [urgencyResult, titleResult, descriptionResult] =
      await Promise.allSettled([
        this.aiService.detectUrgency(event.text, availablePriorities),
        this.aiService.generateTicketTitle(event.text, platformTeam.id),
        this.aiService.generateTicketDescription(event.text, platformTeam.id),
      ]);

    // Use fallbacks if AI operations fail
    const urgency =
      urgencyResult.status === 'fulfilled' ? urgencyResult.value : 'Medium';

    const title =
      titleResult.status === 'fulfilled' ? titleResult.value : event.text;

    const description =
      descriptionResult.status === 'fulfilled'
        ? descriptionResult.value
        : event.text;

    this.logger.debug(`Received urgency: ${urgency}`);
    this.logger.debug(`Received title: ${title}`);
    this.logger.debug(`Received description: ${description}`);
    this.logger.debug(`Type of description: ${typeof description}`);

    // Get the priority id - pass null if no priorities available
    let priorityId: string | undefined;
    if (getPrioritiesResponse.length > 0) {
      priorityId = getPrioritiesResponse.find(
        (p) => p.name === urgency,
      )?.id;
    }

    return {
      urgency,
      title,
      description,
      priorityId,
      parsedDescription: description,
    };
  }

  /**
   * Check if the text is a valid ticket
   * @param text Text
   * @returns True if the text is a valid ticket, false otherwise
   */
  async aiCheckIsValidTicket(
    text: string,
    platformTeam: PlatformTeams,
    installation: Installations,
  ): Promise<boolean> {
    // Get the AI provider and model
    const aiModel = await this.settingsCore.getValue('ai_model', {
      platformTeam,
      workspace: installation,
    });

    this.logger.log(
      `Using AI model ${aiModel} for ticket validation, for installation ${installation.id}, platform team ${platformTeam.uid}`,
    );

    // Set the AI provider and model
    this.setAiProviderAndModel(aiModel);

    // Check if the text is a valid ticket
    const requiresSupportTicket = await this.aiService.isValidTicket(
      text,
      platformTeam.id,
    );

    this.logger.log(`Requires support ticket: ${requiresSupportTicket}`);

    return requiresSupportTicket;
  }

  private setAiProviderAndModel(aiModel: string) {
    // Set the active provider based on the model
    if (
      aiModel === 'claude-3-7-sonnet-20250219' ||
      aiModel === 'claude-3-5-haiku-20241022'
    ) {
      // Set the active provider
      this.aiService.setActiveProvider('claude');
      this.aiService.setActiveModel(aiModel);
    } else if (aiModel === 'gpt-4o' || aiModel === 'o3-mini-2025-01-31') {
      // Set the active provider
      this.aiService.setActiveProvider('openai');
      this.aiService.setActiveModel(aiModel);
    } else {
      // Default to OpenAI GPT-o3-mini
      this.aiService.setActiveProvider('openai');
      this.aiService.setActiveModel('o3-mini-2025-01-31');
    }
  }

  async createTicketForTeam(
    installation: Installations,
    channel: Channels,
    customer: Person,
    event: KnownEventFromType<'message'>,
    commentPayload?: Omit<CreateNewComment, 'ticketId'>,
  ) {
    // TypeGuard: Validate that the message has text to fetch the union type [[Possibly] A Top Level Message]
    const topLevelMessage = 'text' in event;
    if (!topLevelMessage) {
      throw new Error('Message is not a top level message');
    }

    // TypeGuard: Validate that the message has a team
    if (!('team' in event)) {
      throw new Error('Slack workspace not attached to the event message!');
    }

    // Get the timestamp of the message
    const { ts, event_ts } = event;
    let threadTs: string | undefined;
    if ('thread_ts' in event) {
      threadTs = event.thread_ts;
    }

    // Get the platform team from the channel
    let team: PlatformTeams | null = null;

    // Get the primary team
    const primaryTeam = channel.platformTeamsToChannelMappings.find(
      (mapping) => mapping.relationshipType === TeamRelationshipType.PRIMARY,
    );

    // If there is a primary team, use it
    if (primaryTeam) {
      team = primaryTeam.platformTeam;
    } else if (channel.platformTeamsToChannelMappings.length === 1) {
      team = channel.platformTeamsToChannelMappings[0].platformTeam;
    }

    if (!team) {
      throw new Error('No primary platform team mapping found for channel!');
    }

    // Check if the bot is mentioned
    const { mentionedUserGroups } = getSlackMentions(installation, event.text);

    // Get the sub team id
    let subTeamId: string | undefined;

    // If there are mentioned user groups, we need to check if they are mapped to a platform team
    if (mentionedUserGroups.length > 0) {
      // Check if the mentioned user groups exist and are mapped to a platform team
      const mappedSubTeams = await this.subGroupsMapsRepository.findAll({
        where: {
          subGroup: { slackGroupId: In(mentionedUserGroups) },
          installation: { id: installation.id },
          deletedAt: IsNull(),
        },
        relations: { subGroup: true },
      });

      // If the mentioned user group is mapped to a platform team, we will use that team id
      if (mappedSubTeams.length > 0) {
        subTeamId = mappedSubTeams[0].platformSubTeam;
      }
    }

    this.logger.log('Getting ticket details from AI');

    // Get the AI model
    const aiModel = await this.settingsCore.getValue('ai_model', {
      recursivelyLookup: false,
      platformTeam: team,
      workspace: installation,
    });

    let ticketUrgency: string | undefined;
    let ticketTitle: string | undefined;
    let ticketPriorityId: string | undefined;
    let ticketParsedDescription: any;

    // If the AI model is not set, we will not create a ticket
    if (!aiModel) {
      this.logger.debug(
        `AI model is not set for team ${team.uid}! Using fallback ticket details.`,
      );

      ticketTitle = `${event.text.slice(0, 117)}...`;
      ticketParsedDescription = {
        ticket_description: `${event.text.slice(117)}...`,
      };
    } else {
      try {
        const { urgency, title, priorityId, parsedDescription } =
          await this.aiGetTicketDetails(installation, team, event);

        ticketUrgency = urgency;
        ticketTitle = title;
        ticketPriorityId = priorityId;
        ticketParsedDescription = parsedDescription;
      } catch (error) {
        this.logger.error(
          `Failed to get AI ticket details, using fallback: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );

        // Use fallback values if AI processing fails completely
        ticketTitle = event.text.length > 117 ? `${event.text.slice(0, 117)}...` : event.text;
        ticketParsedDescription = event.text.length > 117 ? event.text.slice(117) : event.text;
        ticketUrgency = 'Medium'; // Default urgency
        ticketPriorityId = undefined; // Pass null for priority
      }
    }

    this.logger.log('Creating ticket with comment in platform');

    // Create ticket in platform
    const ticketPayload: CreateNewTicket = {
      requestorEmail: customer.slackProfileEmail,
      text: event.text,
      title: ticketTitle,
      subTeamId,
      description:
        typeof ticketParsedDescription === 'object'
          ? (ticketParsedDescription?.ticket_description ??
            ticketParsedDescription?.ticketDescription)
          : ticketParsedDescription,
      urgency: ticketUrgency ? ticketPriorityId : undefined,
      teamId: team.uid,
      performRouting: true,
      metadata: {
        slack: {
          channel: event.channel,
          ts: threadTs ?? event.ts,
          user: event.user,
        },
      },
    };

    if (commentPayload) {
      const commentBody = await this.platformApiProvider.constructCommentBody(
        installation,
        commentPayload,
      );

      ticketPayload.commentContent = commentBody.content;
      ticketPayload.commentContentHtml = commentBody.contentHtml;
      ticketPayload.commentContentJson = commentBody.contentJson;
      ticketPayload.commentAttachmentIds = commentBody.attachmentIds;
      ticketPayload.commentMetadata = commentBody.metadata;
      ticketPayload.commentImpersonatedUserEmail =
        commentBody.impersonatedUserEmail;
      ticketPayload.commentImpersonatedUserName =
        commentBody.impersonatedUserName;
      ticketPayload.commentImpersonatedUserAvatar =
        commentBody.impersonatedUserAvatar;
    }

    // Create a new ticket on Platform, we need to map this back to a request
    const ticket = await this.platformApiProvider.createNewTicket(
      installation,
      ticketPayload,
    );

    // Get the permalink for the message
    const messagePermalink = await this.slackWebAPIService.getPermalink(
      installation.botToken,
      { channel: event.channel, message_ts: ts },
    );

    // If the message permalink is not ok, throw an error
    if (!messagePermalink.ok) {
      throw new Error(messagePermalink.error);
    }

    return { ticket, event_ts, threadTs, messagePermalink, ts };
  }

  private async getChannel(
    installation: Installations,
    event: KnownEventFromType<'message'>,
  ) {
    this.logger.log(`Looking up the channel ${event.channel} if it exists!`);

    // Get or upsert the channel
    const channel = await this.channelsRepository.findByCondition({
      where: {
        channelId: event.channel,
        installation: { id: installation.id },
      },
      relations: {
        platformTeamsToChannelMappings: {
          platformTeam: true,
        },
      },
    });

    if (!channel) {
      this.logger.debug(`Channel ${event.channel} not found in the database!`);
      throw new Error('Channel not found!');
    }

    // If the channel is not a customer channel, we will not create a ticket
    if (
      channel.channelType !== ChannelType.CUSTOMER_CHANNEL &&
      channel.channelType !== ChannelType.INTERNAL_HELPDESK
    ) {
      this.logger.debug(
        `Channel ${channel.channelId} is not a customer channel! Skipping ticket creation.`,
      );
      return;
    }

    // If the bot is not active we won't proceed further
    if (!channel.isBotActive) {
      this.logger.debug(
        `Channel ${channel.channelId} is not active! Skipping ticket creation.`,
      );
      return;
    }

    this.logger.log(`Channel ${channel.channelId} found in the database!`);

    return channel;
  }

  private async getUser(
    installation: Installations,
    event: KnownEventFromType<'message'>,
    channel: Channels,
  ) {
    let isCustomer = false;
    if (!('user' in event)) {
      throw new Error('User not found!');
    }

    let userInQuestion: Person = await this.usersRepository.findOne({
      where: {
        slackId: event.user,
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
      },
    });

    if (!userInQuestion) {
      userInQuestion = await this.customerContactsRepository.findOne({
        where: {
          slackId: event.user,
          installation: { id: installation.id },
        },
      });

      // If the user is a customer contact, we will set the isCustomer flag to true
      if (userInQuestion) {
        isCustomer = true;
      }
    }

    if (!userInQuestion) {
      userInQuestion =
        await this.slackAppManagementService.upsertPersonWithIdentification(
          event.user,
          installation,
          channel,
        );

      if (userInQuestion instanceof CustomerContacts) {
        isCustomer = true;
      } else if (userInQuestion instanceof Users) {
        isCustomer = false;
      }
    }

    this.logger.log(
      `User ${userInQuestion.slackId} is a ${isCustomer ? 'customer' : 'user'}`,
    );

    // If the user is not a customer, we will not create a ticket
    if (!isCustomer && channel.channelType !== ChannelType.INTERNAL_HELPDESK) {
      this.logger.debug(
        `User ${userInQuestion.slackId} is not a customer! Skipping ticket creation.`,
      );

      return { user: userInQuestion, isCustomer };
    }

    return { user: userInQuestion, isCustomer };
  }

  private async shouldGroupWithExistingConversation(
    installation: Installations,
    channel: Channels,
    userInQuestion: Person,
    event: KnownEventFromType<'message'>,
  ) {
    // Check if we should group this message with an existing conversation
    const { shouldGroup, existingTicketId, slackMessage } =
      await this.conversationGroupingService.shouldGroupWithExistingConversation(
        installation,
        channel,
        userInQuestion,
      );

    this.logger.log(
      `Should group: ${shouldGroup}, Existing ticket ID: ${existingTicketId}`,
    );

    if (shouldGroup && existingTicketId) {
      this.logger.debug(
        `Grouping message with existing ticket ${existingTicketId}`,
      );

      // Create a comment on the existing ticket instead of a new ticket
      const { comment } =
        await this.conversationGroupingService.createCommentOnExistingTicket(
          installation,
          existingTicketId,
          userInQuestion,
          event,
        );

      // Record the grouped message
      await this.groupedSlackMessagesRepository.save({
        slackMessageTs: event.ts,
        channel: { id: channel.id },
        platformTicketId: existingTicketId,
        installation: { id: installation.id },
        parentCommentId: comment.data.id,
        groupedIntoMessage: { id: slackMessage.id },
        organization: { id: installation.organization.id },
      });

      this.logger.log(
        `Grouped message with existing ticket ${existingTicketId}`,
      );

      return { comment, ticket: { id: existingTicketId }, isGrouped: true };
    }

    return { isGrouped: false };
  }
}
