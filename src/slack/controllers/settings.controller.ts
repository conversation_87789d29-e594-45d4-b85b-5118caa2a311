import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiResponse, ApiBody, ApiBearerAuth, ApiSecurity } from '@nestjs/swagger';
import { GetBotCtx } from '../../auth/decorators';
import { AuthGuard } from '../../auth/guards';
import { BotCtx } from '../../auth/interfaces';
import { UpdateSettingsDTO } from '../dtos/settings.dto';
import { SettingsService } from '../services/settings.service';

@Controller('v1/slack/settings')
@UseGuards(AuthGuard)
@ApiTags('Settings')
@ApiBearerAuth()
@ApiSecurity('x-api-key')
export class SettingsController {
  constructor(private readonly settingsService: SettingsService) {}

  @Get('/teams/:teamId')
  @ApiOperation({ summary: 'Get team settings', description: 'Retrieves all settings for a specific team' })
  @ApiParam({ name: 'teamId', description: 'Platform team ID', required: true })
  @ApiResponse({ status: 200, description: 'Team settings retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Team not found' })
  async getTeamSettings(
    @Param('teamId') teamId: string,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    return this.settingsService.getTeamSettings(teamId, botCtx);
  }

  @Put('/teams/:teamId')
  @ApiOperation({ summary: 'Update team settings', description: 'Updates settings for a specific team' })
  @ApiParam({ name: 'teamId', description: 'Platform team ID', required: true })
  @ApiBody({ type: UpdateSettingsDTO, description: 'Settings to update' })
  @ApiResponse({ status: 200, description: 'Settings updated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Team not found' })
  async updateSettings(
    @Param('teamId') teamId: string,
    @Body() updateDto: UpdateSettingsDTO,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    return this.settingsService.updateSettings(teamId, updateDto, botCtx);
  }

  @Delete('/teams/:teamId/settings/:key')
  @ApiOperation({ summary: 'Delete a setting', description: 'Deletes a specific setting for a team' })
  @ApiParam({ name: 'teamId', description: 'Platform team ID', required: true })
  @ApiParam({ name: 'key', description: 'Setting key to delete', required: true })
  @ApiResponse({ status: 200, description: 'Setting deleted successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Setting or team not found' })
  async deleteSetting(
    @Param('teamId') teamId: string,
    @Param('key') settingKey: string,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    return this.settingsService.deleteSetting(teamId, settingKey, botCtx);
  }

  @Post('/teams/:teamId/reset')
  @ApiOperation({ summary: 'Reset team settings', description: 'Resets all settings for a team to default values' })
  @ApiParam({ name: 'teamId', description: 'Platform team ID', required: true })
  @ApiResponse({ status: 201, description: 'Settings reset successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Team not found' })
  async resetSettings(
    @Param('teamId') teamId: string,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    return this.settingsService.resetSettings(teamId, botCtx);
  }
}
