import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  InternalServerErrorException,
  NotFoundException,
  Post,
  Query,
} from "@nestjs/common";
import { UseGuards } from "@nestjs/common";
import { Inject } from "@nestjs/common";
import {
  ApiBody,
  ApiOperation,
  ApiResponse,
  ApiSecurity,
  ApiTags,
} from "@nestjs/swagger";
import { ChatPostMessageResponse } from "@slack/web-api";
import { GetBotCtx } from "../../auth/decorators";
import { NoSlackTeam } from "../../auth/decorators/no-slack-team.decorator";
import { AuthGuard } from "../../auth/guards";
import { BotCtx } from "../../auth/interfaces";
import { TransactionService } from "../../database/common/transactions.service";
import { ChannelType } from "../../database/entities/channels/channels.entity";
import { ChannelsRepository } from "../../database/entities/channels/repositories/channels.repository";
import { TeamRelationshipType } from "../../database/entities/mappings/platform-teams-to-channel-mappings.entity";
import { TeamChannelMapsRepository } from "../../database/entities/mappings/repositories/team-channel-maps.repository";
import { TeamsRepository } from "../../database/entities/teams/repositories/teams.repository";
import { ILogger } from "../../utils";
import { CUSTOM_LOGGER_TOKEN } from "../../utils";
import {
  AddReactionDTO,
  AddReactionResponseDTO,
  DeleteMessageDTO,
  DeleteMessageResponseDTO,
  InviteUserToConversationDTO,
  InviteUserToConversationResponseDTO,
  JoinConversationDTO,
  JoinConversationResponseDTO,
  KickUserFromConversationDTO,
  KickUserFromConversationResponseDTO,
  LeaveConversationDTO,
  LeaveConversationResponseDTO,
  PostMessageDTO,
  PostMessageResponseDTO,
  RemoveReactionDTO,
  RemoveReactionResponseDTO,
  UpdateMessageDTO,
  UpdateMessageResponseDTO,
} from "../dtos/activities.dto";
import { SlackWebAPIService } from "../providers/slack-apis/slack-apis.service";
import { GetAllMembersResponseDTO } from "../dtos/members.dto";
import { SlackMembersService } from "../services/members.service";
import { CommonQueryParamsToFetchEntityData } from "../query-params";

@Controller("v1/slack/activities")
@NoSlackTeam()
@UseGuards(AuthGuard)
@ApiTags("SlackActivities")
@ApiSecurity("x-api-key")
export class SlackActivitiesController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly slackWebApiService: SlackWebAPIService,
    private readonly teamsRepository: TeamsRepository,
    private readonly channelsRepository: ChannelsRepository,
    private readonly teamChannelMapsRepository: TeamChannelMapsRepository,
    private readonly slackMembersService: SlackMembersService
  ) {}

  @Post("/post-message")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: "Post a message to Slack",
    description: "Sends a message to a Slack channel",
  })
  @ApiBody({ type: PostMessageDTO })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Message posted successfully",
    type: [PostMessageResponseDTO],
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: "Error posting message",
  })
  async postMessage(
    @Body() body: PostMessageDTO,
    @GetBotCtx() botCtx: BotCtx
  ): Promise<Array<PostMessageResponseDTO>> {
    const { installation, installations } = botCtx;
    const {
      channel: channelId,
      text,
      threadTs,
      blocks,
      unfurlLinks = true,
      unfurlMedia = true,
    } = body;

    try {
      // If an installation is provided via `x-slack-id` then we'll fire only for that team
      if (installation?.botToken) {
        const response = await this.slackWebApiService.sendMessage(
          installation.botToken,
          {
            channel: channelId,
            text,
            thread_ts: threadTs,
            blocks,
            unfurl_links: unfurlLinks,
            unfurl_media: unfurlMedia,
          }
        );

        return [
          {
            ok: response.ok,
            channel: response.channel,
            ts: response.ts,
            message: response.message,
          },
        ];
      }

      // Fanout to all installations
      const responses: Array<ChatPostMessageResponse> = [];
      for (const ins of installations) {
        try {
          const res = await this.slackWebApiService.sendMessage(ins.botToken, {
            channel: channelId,
            text,
            thread_ts: threadTs,
            blocks,
            unfurl_links: unfurlLinks,
            unfurl_media: unfurlMedia,
          });

          responses.push(res);
        } catch (error) {
          if (error instanceof Error) {
            this.logger.error(
              `Error sending message to channel ${channelId}: ${error.message}`
            );

            responses.push({
              ok: false,
              channel: channelId,
              ts: threadTs,
              error: error.message,
            });
          } else {
            console.error(
              `Error sending message to channel ${channelId}`,
              error
            );
          }
        }
      }

      // Format the responses
      const formattedResponses = responses.map((res) => ({
        ok: res.ok,
        channel: res.channel,
        ts: res.ts,
        message: res.message,
      }));

      return formattedResponses;
    } catch (error) {
      this.logger.error(
        `Error sending message to channel ${channelId}: ${error}`
      );

      throw new InternalServerErrorException("Something went wrong");
    }
  }

  @Post("/update-message")
  @ApiOperation({
    summary: "Update a Slack message",
    description: "Updates an existing message in a Slack channel",
  })
  @ApiBody({ type: UpdateMessageDTO })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Message updated successfully",
    type: UpdateMessageResponseDTO,
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: "Error updating message",
  })
  async updateMessage(
    @Body() body: UpdateMessageDTO,
    @GetBotCtx() botCtx: BotCtx
  ): Promise<UpdateMessageResponseDTO> {
    const { channel: channelId, text, ts, blocks } = body;
    try {
      const response = await this.slackWebApiService.updateMessage(
        botCtx.installation.botToken,
        {
          channel: channelId,
          text,
          ts,
          blocks: JSON.parse(blocks),
        }
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Error updating message in channel ${channelId}: ${error}`
      );
      throw new InternalServerErrorException("Something went wrong");
    }
  }

  @Post("/delete-message")
  @ApiOperation({
    summary: "Delete a Slack message",
    description: "Deletes a message from a Slack channel",
  })
  @ApiBody({ type: DeleteMessageDTO })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Message deleted successfully",
    type: DeleteMessageResponseDTO,
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: "Error deleting message",
  })
  async deleteMessage(
    @Body() body: DeleteMessageDTO,
    @GetBotCtx() botCtx: BotCtx
  ): Promise<DeleteMessageResponseDTO> {
    const { channel: channelId, ts } = body;
    try {
      const response = await this.slackWebApiService.deleteMessage(
        botCtx.installation.botToken,
        {
          channel: channelId,
          ts,
        }
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Error deleting message in channel ${channelId}: ${error}`
      );
      throw new InternalServerErrorException("Something went wrong");
    }
  }

  @Post("/add-reaction")
  @ApiOperation({
    summary: "Add a reaction to a message",
    description: "Adds an emoji reaction to a Slack message",
  })
  @ApiBody({ type: AddReactionDTO })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Reaction added successfully",
    type: AddReactionResponseDTO,
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: "Error adding reaction",
  })
  async addReaction(
    @Body() body: AddReactionDTO,
    @GetBotCtx() botCtx: BotCtx
  ): Promise<AddReactionResponseDTO> {
    const { channel, ts, name } = body;
    try {
      const response = await this.slackWebApiService.addReactionToMessage(
        botCtx.installation.botToken,
        {
          channel,
          timestamp: ts,
          name,
        }
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Error adding reaction to message in channel ${channel}: ${error}`
      );
      throw new InternalServerErrorException("Something went wrong");
    }
  }

  @Post("/remove-reaction")
  @ApiOperation({
    summary: "Remove a reaction from a message",
    description: "Removes an emoji reaction from a Slack message",
  })
  @ApiBody({ type: RemoveReactionDTO })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Reaction removed successfully",
    type: RemoveReactionResponseDTO,
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: "Error removing reaction",
  })
  async removeReaction(
    @Body() body: RemoveReactionDTO,
    @GetBotCtx() botCtx: BotCtx
  ): Promise<RemoveReactionResponseDTO> {
    const { channel, ts, name } = body;
    try {
      const response = await this.slackWebApiService.removeReactionFromMessage(
        botCtx.installation.botToken,
        {
          channel,
          timestamp: ts,
          name,
        }
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Error removing reaction from message in channel ${channel}: ${error}`
      );
      throw new InternalServerErrorException("Something went wrong");
    }
  }

  @Post("/invite-user-to-conversation")
  @ApiOperation({
    summary: "Invite a user to a conversation",
    description: "Invites a user to a Slack channel or group",
  })
  @ApiBody({ type: InviteUserToConversationDTO })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "User invited successfully",
    type: InviteUserToConversationResponseDTO,
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: "Error inviting user",
  })
  async inviteUserToConversation(
    @Body() body: InviteUserToConversationDTO,
    @GetBotCtx() botCtx: BotCtx
  ): Promise<InviteUserToConversationResponseDTO> {
    const { channel, user } = body;
    try {
      const response = await this.slackWebApiService.inviteUserToConversation(
        botCtx.installation.botToken,
        {
          channel,
          users: user,
        }
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Error inviting user to conversation in channel ${channel}: ${error}`
      );
      throw new InternalServerErrorException("Something went wrong");
    }
  }

  @Post("/kick-user-from-conversation")
  @ApiOperation({
    summary: "Remove a user from a conversation",
    description: "Removes a user from a Slack channel or group",
  })
  @ApiBody({ type: KickUserFromConversationDTO })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "User removed successfully",
    type: KickUserFromConversationResponseDTO,
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: "Error removing user",
  })
  async kickUserFromConversation(
    @Body() body: KickUserFromConversationDTO,
    @GetBotCtx() botCtx: BotCtx
  ): Promise<KickUserFromConversationResponseDTO> {
    const { channel, user } = body;
    try {
      const response = await this.slackWebApiService.kickUserFromConversation(
        botCtx.installation.botToken,
        {
          channel,
          user,
        }
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Error kicking user from conversation in channel ${channel}: ${error}`
      );
      throw new InternalServerErrorException("Something went wrong");
    }
  }

  @Post("/join-conversation")
  @ApiOperation({
    summary: "Join a conversation",
    description: "Joins a Slack channel",
  })
  @ApiBody({ type: JoinConversationDTO })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Joined conversation successfully",
    type: JoinConversationResponseDTO,
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: "Error joining conversation",
  })
  async joinConversation(
    @Body() body: JoinConversationDTO,
    @GetBotCtx() botCtx: BotCtx
  ): Promise<JoinConversationResponseDTO> {
    const { channel, platformTeam, channelType } = body;
    try {
      const team = await this.teamsRepository.findByCondition({
        where: {
          uid: platformTeam,
        },
      });
      if (!team) {
        throw new NotFoundException("Team not found");
      }

      // Upsert channel entity
      await this.channelsRepository.upsert(
        {
          channelId: channel,
          installation: { id: botCtx.installation.id },
          organization: { id: team.organization.id },
          isBotJoined: true,
          isBotActive: true,
          channelType: channelType || ChannelType.NOT_CONFIGURED,
        },
        {
          conflictPaths: ["channelId", "installationId"],
          skipUpdateIfNoValuesChanged: true,
        }
      );

      const savedChannel = await this.channelsRepository.findByCondition({
        where: {
          channelId: channel,
          installation: { id: botCtx.installation.id },
          organization: { id: team.organization.id },
        },
      });

      if (!savedChannel) {
        throw new InternalServerErrorException("Saving channel entity failed");
      }

      const channelMappingExists = await this.teamChannelMapsRepository.exists({
        where: {
          platformTeam: { id: team.id },
          installation: { id: botCtx.installation.id },
          organization: { id: team.organization.id },
        },
      });

      // Upsert team channel mapping entity
      await this.teamChannelMapsRepository.upsert(
        {
          relationshipType: channelMappingExists
            ? TeamRelationshipType.SECONDARY
            : TeamRelationshipType.PRIMARY,
          platformTeam: team,
          channel: savedChannel,
          installation: team.installation,
          organization: team.organization,
        },
        {
          conflictPaths: ["platformTeam", "channel"],
          skipUpdateIfNoValuesChanged: true,
        }
      );

      // This needs to be done at the end of transaction, as there is no way to rollback this once done.
      return await this.slackWebApiService.joinConversation(
        botCtx.installation.botToken,
        {
          channel: savedChannel.channelId,
        }
      );
    } catch (error) {
      this.logger.error(
        `Error joining conversation in channel ${channel}: ${error}`
      );
      throw new InternalServerErrorException("Something went wrong");
    }
  }
  @NoSlackTeam()
  @Get("/members")
  @ApiOperation({
    summary: "Get all members",
    description: "Retrieves a list of all members in the Slack workspace",
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Members retrieved successfully",
    type: GetAllMembersResponseDTO,
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: "Error retrieving members",
  })
  async getAllMembers(
    @Query() query: CommonQueryParamsToFetchEntityData,
    @GetBotCtx() botCtx: BotCtx
  ): Promise<GetAllMembersResponseDTO> {
    try {
      const response = await this.slackMembersService.getAllMembers(
        botCtx,
        query
      );
      return response;
    } catch (error) {
      this.logger.error(`Error retrieving members: ${error}`);
      throw new InternalServerErrorException("Something went wrong");
    }
  }

  @Post("/leave-conversation")
  @ApiOperation({
    summary: "Leave a conversation",
    description: "Leaves a Slack channel",
  })
  @ApiBody({ type: LeaveConversationDTO })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Left conversation successfully",
    type: LeaveConversationResponseDTO,
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: "Error leaving conversation",
  })
  async leaveConversation(
    @Body() body: LeaveConversationDTO,
    @GetBotCtx() botCtx: BotCtx
  ): Promise<LeaveConversationResponseDTO> {
    const { channel } = body;
    try {
      const response = await this.slackWebApiService.leaveConversation(
        botCtx.installation.botToken,
        {
          channel,
        }
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Error leaving conversation in channel ${channel}: ${error}`
      );
      throw new InternalServerErrorException("Something went wrong");
    }
  }
}
