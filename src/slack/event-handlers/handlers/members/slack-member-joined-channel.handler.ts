import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { get } from 'lodash';
import { Repository } from 'typeorm';
import {
  Channels,
  CustomerContacts,
  Installations,
  Users,
} from '../../../../database/entities';
import { ChannelsRepository } from '../../../../database/entities/channels/repositories';
import { EmittableSlackEvents } from '../../../../external/provider/constants/platform-events.constants';
import { CreateCustomObjectRecord } from '../../../../external/provider/interfaces/custom-objects.interface';
import { PlatformCustomerContact } from '../../../../external/provider/interfaces/customer-contacts.interface';
import { ThenaAppsPlatformApiProvider } from '../../../../external/provider/thena-apps-platform-api.provider';
import { ThenaPlatformApiProvider } from '../../../../external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';
import { SlackAppManagementService } from '../../../core/management';
import { BotChannelJoinedHandler } from '../../../core/slack-channel';
import { SlackEvent } from '../../../decorators';
import { BaseSlackEventHandler, SlackEventMap } from '../../interface';

const SPAN_ID = 'slack-member-joined-channel';

@Injectable()
@SlackEvent('member_joined_channel')
export class SlackMemberJoinedChannelHandler extends BaseSlackEventHandler<'member_joined_channel'> {
  eventType = 'member_joined_channel' as const;

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly botChannelJoinedHandler: BotChannelJoinedHandler,
    private readonly slackAppManagementService: SlackAppManagementService,

    @InjectRepository(Users)
    private readonly userRepository: Repository<Users>,

    @InjectRepository(CustomerContacts)
    private readonly customerContactRepository: Repository<CustomerContacts>,
    private readonly channelsRepository: ChannelsRepository,

    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,
    private readonly thenaAppsPlatformApiProvider: ThenaAppsPlatformApiProvider,
  ) {
    super();
  }

  canHandle(event: SlackEventMap['member_joined_channel']): boolean {
    return event.event.type === 'member_joined_channel';
  }

  async handle(e: SlackEventMap['member_joined_channel']): Promise<void> {
    try {
      // Handle the bot joining the channel
      const wasThenaBot = await this.botChannelJoinedHandler.handleEvent(e);
      if (wasThenaBot) {
        this.logger.debug(
          `${SPAN_ID} Received 'member_joined_channel' event for team ${e.context.installation.teamId} for user ${e.event.user} in channel ${e.event.channel} at ${e.event.event_ts} but the user is the Thena bot`,
        );

        return;
      }

      // Handle the user joining the channel, note that this could either be a normal user or a customer user
      const { event, context } = e;
      const { user, channel, team } = event;
      const { installation } = context;

      // [SANITY CHECK] If the team id doesn't match, then we don't know what to do
      this.logger.debug(
        `${SPAN_ID} Received 'member_joined_channel' event for team ${team} but the bot is attached to team ${context.installation.teamId}, possibly a customer contact!`,
      );

      // Find the channel in the database
      const foundChannel = await this.channelsRepository.findByCondition({
        where: { channelId: channel, installation: { id: installation.id } },
      });

      this.logger.log(
        `${SPAN_ID} Found channel ${foundChannel} for channel ${channel} in installation ${installation.id}`,
      );

      // Find the person who joined the channel; do we already know about them?
      let personJoined = await this.findPerson(user, context.installation);

      this.logger.log(
        `${SPAN_ID} Found person ${personJoined.person.id} for user ${user} in installation ${installation.id}`,
      );

      // If we don't know about them, then we need to upsert them
      if (!personJoined) {
        const upsertResult =
          await this.slackAppManagementService.upsertPersonWithIdentification(
            user,
            installation,
            foundChannel,
          );

        personJoined = {
          type: upsertResult instanceof CustomerContacts ? 'customer' : 'user',
          person: upsertResult as Users,
        };
      }

      this.logger.log(
        `${SPAN_ID} Found person ${personJoined.person.id} for user ${user} in installation ${installation.id}`,
      );

      if (personJoined.type === 'customer') {
        try {
          await this.syncPlatform(
            installation,
            foundChannel,
            personJoined.person as CustomerContacts,
          );

          // Update the customer contact with the new channel
          await this.customerContactRepository.update(personJoined.person.id, {
            channels: [
              ...(personJoined.person as CustomerContacts).channels,
              foundChannel,
            ],
          });

          this.logger.log(
            `${SPAN_ID} Updated customer contact ${personJoined.person.id} with new channel ${foundChannel.id}`,
          );
        } catch (error) {
          this.logger.error(
            `${SPAN_ID} Failed to sync platform for customer contact ${personJoined.person.id}. Error: ${error}`,
          );
        }
      }

      // Post the event to the platform
      await this.thenaAppsPlatformApiProvider.postEventsToPlatform(
        installation.organization,
        {
          ...event,
          type: EmittableSlackEvents.MEMBER_JOINED_CHANNEL,
          userInfo: {
            id: personJoined.person.id,
            email: personJoined.person.slackProfileEmail,
            name:
              personJoined.person.displayName ?? personJoined.person.realName,
            avatar: personJoined.person.images?.image_48,
          },
          userType:
            personJoined.type === 'customer' ? 'customer' : 'internal_member',
        },
      );

      this.logger.log(
        `${SPAN_ID} Posted event to platform for team ${installation.teamId}`,
      );

      return;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `${SPAN_ID} Failed to handle 'member_joined_channel' event for team ${e.context.installation.teamId} for user ${e.event.user} in channel ${e.event.channel} at ${e.event.event_ts}`,
          error.stack,
        );
      } else {
        console.error(
          `${SPAN_ID} Failed to handle 'member_joined_channel' event for team ${e.context.installation.teamId} for user ${e.event.user} in channel ${e.event.channel} at ${e.event.event_ts}`,
          error,
        );
      }
    }
  }

  /**
   * Find the person who joined the channel do we already know about them?
   * @param userId The user ID from Slack
   * @param installation The installation from the context
   * @returns The person who joined the channel
   */
  private async findPerson(userId: string, installation: Installations) {
    // Check if the user is a customer contact
    const customerContact = await this.customerContactRepository.findOne({
      where: { slackId: userId, installation: { id: installation.id } },
    });

    // If the user is a customer contact, return that
    if (customerContact) {
      return { type: 'customer', person: customerContact };
    }

    // Check if the user is a user
    const user = await this.userRepository.findOne({
      where: { slackId: userId, installation: { id: installation.id } },
    });

    // If the user is a user, return that
    if (user) {
      return { type: 'user', person: user };
    }

    // If the user is not a customer contact or a user, then we don't know what to do
    return null;
  }

  private async syncPlatform(
    installation: Installations,
    channel: Channels,
    contact: CustomerContacts,
  ) {
    let platformContact: PlatformCustomerContact;

    // Get existing platform dump data
    const platformContactId = get(contact, 'platformDump.customerContactId');
    const existingCustomObjectRecordIds = get(
      contact,
      'platformDump.customObjectRecordIds',
      [],
    );

    // Get the custom object IDs
    const contactCustomObjectId = get(
      installation.platformDump,
      'customObjects.contactCustomObjectId',
    );

    // Get the custom field Ids
    const {
      slackChannelId: slackChannelIdCustomFieldId,
      slackChannelName: slackChannelNameCustomFieldId,
      slackTeamId: slackTeamIdCustomFieldId,
      slackTeamName: slackTeamNameCustomFieldId,
      slackUserId: slackUserIdCustomFieldId,
      customerContactId: customerContactIdCustomFieldId,
    } = get(installation.platformDump, 'customFields', {
      slackChannelId: null,
      slackChannelName: null,
      slackTeamId: null,
      slackTeamName: null,
      slackUserId: null,
      customerContactId: null,
    });

    if (platformContactId) {
      // Get existing platform contact

      const platformContacts =
        await this.thenaPlatformApiProvider.getCustomerContactsByIds(
          installation,
          [platformContactId],
        );

      if (platformContacts.length === 0) {
        this.logger.error(
          `Customer contact ${contact.id} has no platform contact ID`,
        );
        return;
      }

      platformContact = platformContacts[0];

      // Update the metadata
      const newMetadata = {
        ...platformContact.metadata,
        sinks: {
          ...platformContact.metadata.sinks,
          slack: {
            ...platformContact.metadata.sinks.slack,
            lastSyncedAt: new Date().toISOString(),
            channels: [
              ...platformContact.metadata.sinks.slack.channels,
              {
                channelId: channel.channelId,
                channelName: channel.name,
                teamId: installation.teamId,
                teamName: installation.teamName,
              },
            ],
          },
        },
      };

      // Update the platform contact
      await this.thenaPlatformApiProvider.updateCustomerContact(
        installation,
        platformContact.id,
        { metadata: newMetadata },
      );
    } else {
      // Create customer contact on platform
      platformContact =
        await this.thenaPlatformApiProvider.createCustomerContact(
          installation,
          {
            firstName: contact.name,
            lastName: '',
            email: contact.slackProfileEmail,
            phoneNumber: contact.slackProfilePhone,
            avatarUrl: contact.images?.image_original,
            metadata: {
              sinks: {
                slack: {
                  syncStatus: 'success',
                  lastSyncedAt: new Date(),
                  userId: contact.id,
                  channels: [
                    {
                      channelId: channel.channelId,
                      channelName: channel.name,
                      teamId: installation.teamId,
                      teamName: installation.teamName,
                    },
                  ],
                },
              },
            },
          },
        );
    }

    // Create the custom object record
    const createCustomObjectRecord: CreateCustomObjectRecord = {
      customObjectId: contactCustomObjectId,
      customFieldValues: [
        {
          customFieldId: slackUserIdCustomFieldId,
          data: [{ value: contact.id }],
        },
        {
          customFieldId: slackChannelIdCustomFieldId,
          data: [{ value: channel.channelId }],
        },
        {
          customFieldId: slackChannelNameCustomFieldId,
          data: [{ value: channel.name }],
        },
        {
          customFieldId: slackTeamIdCustomFieldId,
          data: [{ value: installation.teamId }],
        },
        {
          customFieldId: slackTeamNameCustomFieldId,
          data: [{ value: installation.teamName }],
        },
        {
          customFieldId: customerContactIdCustomFieldId,
          data: [{ value: platformContact.id }],
        },
      ],
    };
    const customObjectRecord =
      await this.thenaPlatformApiProvider.createCustomObjectRecord(
        installation,
        createCustomObjectRecord,
      );

    // Update the customer contact with the platform contact ID
    await this.customerContactRepository.update(contact.id, {
      platformDump: {
        customerContactId: platformContact.id,
        customObjectRecordIds: [
          ...existingCustomObjectRecordIds,
          customObjectRecord.id,
        ],
      },
    });
  }
}
