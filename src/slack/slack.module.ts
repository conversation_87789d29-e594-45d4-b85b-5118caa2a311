import { Module } from '@nestjs/common';
import { DiscoveryService } from '@nestjs/core';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CommonModule } from '../common/common.module';
import { Installations } from '../database/entities';
import { InstallationRepository } from '../database/entities/installations/repositories';
import { SlackActionDiscoveryService } from './services/slack-action-discovery.service';
import { SlackService } from './services/slack.service';
import { slackProviders } from './slack-common.providers';
import { TypeORMInstallationStore } from './stores';

@Module({
  imports: [
    CommonModule,
    TypeOrmModule.forFeature([Installations, InstallationRepository]),
  ],
  providers: [
    InstallationRepository,
    DiscoveryService,
    SlackService,
    TypeORMInstallationStore,
    SlackActionDiscoveryService,
    ...slackProviders,
  ],
})
export class SlackModule {}
