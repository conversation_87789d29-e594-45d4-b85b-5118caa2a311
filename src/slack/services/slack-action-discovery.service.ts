import { Inject, Injectable, type OnModuleInit } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { SQSProducerService } from '../../utils/aws-utils/sqs';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils/logger';
import { SLACK_SQS_PRODUCER_SERVICE_TOKEN, SlackEvents } from '../constants';
import { SlackService } from './slack.service';

@Injectable()
export class SlackActionDiscoveryService implements OnModuleInit {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly slackService: SlackService,

    @Inject(SLACK_SQS_PRODUCER_SERVICE_TOKEN)
    private readonly sqsProducerService: SQSProducerService,
  ) {}

  onModuleInit() {
    // Register Slack events
    this.registerEvents();
  }

  /**
   * Register Slack `event` handlers
   */
  private registerEvents() {
    const bolt = this.slackService.getBolt();

    // Register Slack event handlers
    for (const id of SlackEvents) {
      this.logger.log(`Registering event handler for ${id}`);

      bolt.event(id, async (args) => {
        this.logger.log(`Received event: ${id}, ${args.event.type}`);

        const PREFIX_EVENT_ID = 'slack_event';

        // Create the message attributes
        const messageAttrs = {
          event_id: { DataType: 'String', StringValue: uuidv4() },
          event_identifier: { DataType: 'String', StringValue: `${id}` },
          event_name: { DataType: 'String', StringValue: args.event.type },
        } as const;

        // !! Be careful note that we have `_` (underscore) in the message group IDs
        // Create the message group ID
        let messageGroupId = `${PREFIX_EVENT_ID}_${args.body.team_id}`;
        if ('channel' in args.event && typeof args.event.channel === 'string') {
          messageGroupId = `${PREFIX_EVENT_ID}_${args.body.team_id}_${args.event.channel}`;
        } else if (
          'channel' in args.event &&
          typeof args.event.channel === 'object'
        ) {
          messageGroupId = `${PREFIX_EVENT_ID}_${args.body.team_id}_${args.event.channel.id}`;
        }

        try {
          // Send the event to the SQS Queue
          await this.sqsProducerService.sendMessage(
            JSON.stringify(args),
            messageAttrs,
            messageGroupId,
          );
        } catch (sqsProducerError) {
          this.logger.error(`Error sending event to SQS: ${sqsProducerError}`);
        }
      });
    }

    this.logger.log('Registered all Slack event handlers...');
  }
}
