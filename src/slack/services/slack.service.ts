import { Inject, Injectable } from '@nestjs/common';
import { App, ExpressReceiver, LogLevel } from '@slack/bolt';
import { Response as ExpressResponse } from 'express';
import { ConfigKeys, ConfigService } from '../../config/config.service';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';
import { SLACK_SCOPES } from '../constants';
import { TypeORMInstallationStore } from '../stores';

@Injectable()
export class SlackService {
  private bolt: App;
  private receiver: ExpressReceiver;

  constructor(
    private readonly configService: ConfigService,
    private readonly installationStore: TypeORMInstallationStore,

    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
  ) {
    // Express receiver
    this.receiver = new ExpressReceiver({
      // Slack app config
      clientId: this.configService.get(ConfigKeys.SLACK_CLIENT_ID),
      stateSecret: this.configService.get(ConfigKeys.SLACK_STATE_SECRET),
      clientSecret: this.configService.get(ConfigKeys.SLACK_CLIENT_SECRET),
      signingSecret: this.configService.get(ConfigKeys.SLACK_SIGNING_SECRET),

      // Slack scopes
      scopes: SLACK_SCOPES,
      installationStore: this.installationStore,

      // Installation options
      installerOptions: {
        directInstall: true,
        stateVerification: false,
        redirectUriPath: '/slack/oauth_redirect',
        callbackOptions: {
          success: (installation, _opts, _req, res) => {
            this.logger.log(
              `Installation success for ${installation.team.id}, ${installation.team.name}`,
            );

            return (res as ExpressResponse).redirect(
              `${this.configService.get(
                ConfigKeys.THENA_WEB_URL,
              )}/organization/settings/sources`,
            );
          },
          failure: () => {
            // TODO: Handle failure in installation
            console.log('failure');
          },
        },
        installPathOptions: {
          beforeRedirection: (req, _res, options) => {
            // Set metadata before redirecting
            options.metadata = JSON.stringify({
              orgId: (req as any).query.orgId,
              installingUser: (req as any).query.installingUser,
            });

            return Promise.resolve(true);
          },
        },
      },

      // Redirect URI
      redirectUri: `${this.configService.get(
        ConfigKeys.SLACK_APP_BASE_URL,
      )}/slack/oauth_redirect`,
    });

    // Bolt app
    this.bolt = new App({
      receiver: this.receiver,
      logLevel: LogLevel.DEBUG,

      // ---- Developer options
      developerMode: true,
      socketMode: false,
    });
  }

  /**
   * Get the Bolt app
   * @returns The Bolt app
   */
  public use() {
    return this.receiver.app;
  }

  getBolt() {
    return this.bolt;
  }
}
