import { CacheModule } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';
import { DiscoveryModule } from '@nestjs/core';
import { APP_FILTER } from '@nestjs/core';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SentryModule } from '@sentry/nestjs/setup';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { CommonModule } from './common/common.module';
import { ApiExceptionFilter } from './common/filters/api-exception.filter';
import { ConfigModule } from './config/config.module';
import { ConfigKeys } from './config/config.service';
import { ConfigService } from './config/config.service';
import { entities } from './database/entities';
import { SlackModule } from './slack/slack.module';

@Module({
  imports: [
    // Register the sentry module
    SentryModule.forRoot(),

    ConfigModule,
    DiscoveryModule,
    CommonModule,

    // Register the typeorm module
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        return {
          type: 'postgres',
          synchronize: false,
          host: configService.get(ConfigKeys.DB_HOST),
          port: Number.parseInt(configService.get(ConfigKeys.DB_PORT)),
          username: configService.get(ConfigKeys.DB_USERNAME),
          password: configService.get(ConfigKeys.DB_PASSWORD),
          database: configService.get(ConfigKeys.DB_NAME),
          entities,
          schema: 'public',
        };
      },
    }),

    // Register the cache module
    CacheModule.register({ isGlobal: true }),

    // Register the slack module
    SlackModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_FILTER,
      useClass: ApiExceptionFilter,
    },
  ],
})
export class AppModule {}
