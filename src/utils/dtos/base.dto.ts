import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, ValidateNested } from 'class-validator';

/**
 * Base pagination metadata DTO
 */
export class PaginationMetaDTO {
  @ApiProperty({
    description: 'Total number of items',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 10,
  })
  totalPages: number;
}

/**
 * Base paginated response DTO
 * @template T - The type of data in the response
 */
export class PaginatedResponseDTO<T> {
  @ApiProperty({
    description: 'Array of items',
    isArray: true,
  })
  data: T[];

  @ApiProperty({
    description: 'Pagination metadata',
    type: PaginationMetaDTO,
  })
  @ValidateNested()
  @Type(() => PaginationMetaDTO)
  meta: PaginationMetaDTO;
}

/**
 * Base query parameters DTO for pagination
 */
export class PaginationQueryDTO {
  @ApiProperty({
    description: 'Page number (1-based)',
    example: 1,
    required: false,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    required: false,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  limit?: number = 10;

  @ApiProperty({
    description: 'Sort field and direction (e.g., createdAt:desc)',
    example: 'createdAt:desc',
    required: false,
  })
  @IsOptional()
  sort?: string;
}

/**
 * Base error response DTO
 */
export class ErrorResponseDTO {
  @ApiProperty({
    description: 'HTTP status code',
    example: 400,
  })
  statusCode: number;

  @ApiProperty({
    description: 'Error message',
    example: 'Bad Request',
  })
  message: string | string[];

  @ApiProperty({
    description: 'Error type',
    example: 'Validation Error',
  })
  error: string;
}
