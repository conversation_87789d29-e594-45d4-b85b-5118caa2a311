import { INestApplication } from "@nestjs/common";
import { DocumentBuilder, OpenAPIObject, SwaggerModule } from "@nestjs/swagger";
import * as fs from 'fs';
import * as yaml from 'js-yaml';
import path from "path";
import { ConfigKeys, ConfigService } from "../config/config.service";
import { getSchemaPath } from "@nestjs/swagger";
import { SwaggerEnhancerPlugin } from "./plugins/swagger-enhancer.plugin";

// Define interfaces for Swagger schema handling
interface SchemaProperty {
  type?: string;
  description?: string;
  example?: any;
  minimum?: number;
  maximum?: number;
  items?: {
    type: string;
    properties?: Record<string, SchemaProperty>;
  };
}

interface SchemaDefinition {
  description?: string;
  properties?: Record<string, SchemaProperty>;
}

interface SwaggerSchemaDescriptions {
  [key: string]: SchemaDefinition;
}

// Global schema descriptions for reuse across functions
const globalSchemaDescriptions: SwaggerSchemaDescriptions = {
  'UpdateSettingsDTO': {
    description: 'DTO for updating team settings',
    properties: {
      conversationWindow: { description: 'Time window in minutes for conversation context', type: 'number', minimum: 0, maximum: 1440 },
      automaticTickets: { description: 'Whether to automatically create tickets', type: 'boolean' },
      slashCommands: { description: 'Whether to enable slash commands', type: 'boolean' },
      requireForm: { description: 'Whether to require form submission', type: 'boolean' },
      thenaBotTaggingEnabled: { description: 'Whether to enable Thena bot tagging', type: 'boolean' },
      ticketCommand: { description: 'Whether to enable ticket command', type: 'boolean' },
      enableTicketCreationViaReaction: { description: 'Whether to enable ticket creation via reaction', type: 'boolean' },
      aiEnableExtendedThinking: { description: 'Whether to enable extended AI thinking', type: 'boolean' },
      aiModel: { description: 'AI model to use', type: 'string' },
      aiTemperature: { description: 'AI temperature setting', type: 'number' },
      aiMaxTokens: { description: 'Maximum tokens for AI responses', type: 'number' },
      selectedForms: { 
        description: 'Selected forms for the team', 
        type: 'array', 
        items: { type: 'string' } 
      }
    }
  },
  'GetAllMembersResponseDTO': {
    description: 'Response for the get all members endpoint',
    properties: {
      members: { 
        type: 'array', 
        description: 'List of Slack members',
        items: {
          type: 'object'
        }
      },
      total: { type: 'number', example: 100, description: 'Total count of members' },
      page: { type: 'number', example: 1, description: 'Current page number' },
      limit: { type: 'number', example: 10, description: 'Number of items per page' }
    }
  },
  'SlackMemberDTO': {
    description: 'Slack member information',
    properties: {
      id: { type: 'string', example: 'U12345678', description: 'Slack user ID' },
      name: { type: 'string', example: 'johndoe', description: 'Username' },
      deleted: { type: 'boolean', example: false, description: 'Whether the user is deleted' },
      is_bot: { type: 'boolean', example: false, description: 'Whether the user is a bot' },
      profile: { type: 'object', description: 'User profile information' }
    }
  },
  'SlackProfileDTO': {
    description: 'Slack user profile information',
    properties: {
      display_name: { type: 'string', example: 'John Doe', description: 'Display name' },
      real_name: { type: 'string', example: 'John Doe', description: 'Real name' },
      email: { type: 'string', example: '<EMAIL>', description: 'Email address' },
      image_72: { type: 'string', example: 'https://secure.gravatar.com/avatar/123456.jpg', description: 'Profile image' }
    }
  }
};

// OpenAPI specific interfaces to fix TypeScript errors
interface OpenAPIContent {
  'application/json': {
    schema: {
      $ref?: string;
      type?: string;
      properties?: Record<string, any>;
      items?: any;
    }
  };
}

interface OpenAPIResponse {
  description: string;
  content?: OpenAPIContent;
  schema?: any; // Add schema property for direct schema definition
}

interface OpenAPIRequestBody {
  required?: boolean;
  content: OpenAPIContent;
  description?: string;
}

interface OpenAPIParameter {
  name: string;
  in: string;
  required?: boolean;
  schema?: {
    type: string;
  };
}

interface OpenAPIMethod {
  operationId?: string;
  summary?: string;
  description?: string;
  tags?: string[];
  parameters?: OpenAPIParameter[];
  requestBody?: OpenAPIRequestBody;
  responses?: Record<string, OpenAPIResponse>;
}

interface OpenAPIPathItem {
  [method: string]: OpenAPIMethod;
}

export function setupSwagger(app: INestApplication, config: ConfigService) {

  const title = 'Thena Slack API';
  const description = 'API documentation for Thena Slack integration';
  const version = '1.0';
  
  // Define tags with descriptions for better organization
  const tags = [
    { name: 'FormBuilder', description: 'Form builder and submission endpoints' },
    { name: 'SlackActivities', description: 'Slack activities and interactions endpoints' },
    { name: 'Teams', description: 'Slack teams and workspaces endpoints' },
    { name: 'Settings', description: 'Application settings endpoints' },
    { name: 'Authorization', description: 'Authentication and authorization endpoints' },
    { name: 'SlackSync', description: 'Slack data synchronization endpoints' },
    { name: 'Ai', description: 'AI provider and model endpoints' },
    { name: 'Platform', description: 'Platform integration endpoints' },
    { name: 'SubGroups', description: 'Slack subgroups management endpoints' },
    { name: 'TriageRules', description: 'Triage rules management endpoints' },
  ];
  
  const options = new DocumentBuilder()
    .setTitle(title)
    .setDescription(description)
    .setVersion(version)
    .addApiKey({
      type: 'apiKey',
      name: 'X-API-KEY',
      in: 'header',
      description: 'API key for authentication. Must be included in the X-API-KEY header for all requests.'
    }, 'ApiKeyAuth')

  
  // Add all tags
  tags.forEach(tag => {
    options.addTag(tag.name, tag.description);
  });
  
  const document = SwaggerModule.createDocument(app, options.build(), {
    deepScanRoutes: true,
    extraModels: [],
    operationIdFactory: (controllerKey, methodKey) => methodKey,
    ignoreGlobalPrefix: false
  });

  // Apply the Swagger enhancer plugin
  const enhancedDocument = new SwaggerEnhancerPlugin().apply(document);
  
  // Create output file
  createSwaggerOutputFile(enhancedDocument);

  const customOptions = {
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true,
      docExpansion: 'none',
      filter: true,
      showExtensions: true,
      tagsSorter: 'alpha',
      defaultModelsExpandDepth: 3,
      defaultModelExpandDepth: 3,
      displayOperationId: false,
      tryItOutEnabled: true,
    },
    customSiteTitle: title,
    customCss: `
      .swagger-ui .topbar { display: none }
      .swagger-ui .opblock .opblock-summary-description { text-align: right; }
      .swagger-ui .opblock-tag { font-size: 18px; }
      .swagger-ui .opblock .opblock-summary { padding: 8px; }
      .swagger-ui .response-col_description__inner p { margin: 0; }
      .swagger-ui .parameter__name { font-weight: bold; }
      .swagger-ui .parameter__type { font-size: 12px; }
      .swagger-ui .parameter__deprecated { font-size: 12px; }
      .swagger-ui .parameter__in { font-size: 12px; }
      .swagger-ui table tbody tr td:first-of-type { max-width: 20%; }
      .swagger-ui .response-col_status { min-width: 100px; }
    `,
  };

  SwaggerModule.setup("/api", app, enhancedDocument, customOptions);
}

/**
 * Enhances schema definitions in the Swagger document
 * @param document The Swagger document to enhance
 */
function enhanceSchemaDefinitions(document: OpenAPIObject): void {
  // Ensure components exist
  if (!document.components) {
    document.components = {};
  }
  
  // Ensure schemas exist
  if (!document.components.schemas) {
    document.components.schemas = {};
  }
  
  // Ensure parameters exist
  if (!document.components.parameters) {
    document.components.parameters = {};
  }
  
  // Add common parameters
  document.components.parameters['TeamIdParam'] = {
    name: 'teamId',
    in: 'path',
    required: true,
    schema: {
      type: 'string'
    },
    description: 'The ID of the Slack team/workspace',
    example: 'T12345678'
  };
  
  document.components.parameters['ChannelIdParam'] = {
    name: 'channelId',
    in: 'path',
    required: true,
    schema: {
      type: 'string'
    },
    description: 'The ID of the Slack channel',
    example: 'C12345678'
  };
  
  document.components.parameters['UserIdParam'] = {
    name: 'userId',
    in: 'path',
    required: true,
    schema: {
      type: 'string'
    },
    description: 'The ID of the Slack user',
    example: 'U12345678'
  };
  
  document.components.parameters['PageParam'] = {
    name: 'page',
    in: 'query',
    required: false,
    schema: {
      type: 'integer',
      default: 1,
      minimum: 1
    },
    description: 'Page number for pagination',
    example: 1
  };
  
  document.components.parameters['LimitParam'] = {
    name: 'limit',
    in: 'query',
    required: false,
    schema: {
      type: 'integer',
      default: 10,
      minimum: 1,
      maximum: 100
    },
    description: 'Number of items per page',
    example: 10
  };
  
  // Add security schemes
  document.components.securitySchemes = {
    ApiKeyAuth: {
      type: 'apiKey',
      in: 'header',
      name: 'X-API-KEY',
      description: 'API key for authentication. Must be included in the X-API-KEY header for all requests.'
    },
  
  };
  
  // Apply security globally
  document.security = [
    { ApiKeyAuth: [] },
    { BearerAuth: [] }
  ];
  
  // Add descriptions to schemas
  const schemaDescriptions: SwaggerSchemaDescriptions = {
    ...globalSchemaDescriptions,
    'UpdateSettingsDTO': {
      description: 'DTO for updating team settings',
      properties: {
        conversationWindow: { description: 'Time window in minutes for conversation context', type: 'number', minimum: 0, maximum: 1440 },
        automaticTickets: { description: 'Whether to automatically create tickets', type: 'boolean' },
        slashCommands: { description: 'Whether to enable slash commands', type: 'boolean' },
        requireForm: { description: 'Whether to require form submission', type: 'boolean' },
        thenaBotTaggingEnabled: { description: 'Whether to enable Thena bot tagging', type: 'boolean' },
        ticketCommand: { description: 'Whether to enable ticket command', type: 'boolean' },
        enableTicketCreationViaReaction: { description: 'Whether to enable ticket creation via reaction', type: 'boolean' },
        aiEnableExtendedThinking: { description: 'Whether to enable extended AI thinking', type: 'boolean' },
        aiModel: { description: 'AI model to use', type: 'string' },
        aiTemperature: { description: 'AI temperature setting', type: 'number' },
        aiMaxTokens: { description: 'Maximum tokens for AI responses', type: 'number' },
        selectedForms: { description: 'Selected forms for the team', type: 'array', items: { type: 'string' } }
      }
    },
    'MapSubGroupToSubTeamDTO': {
      description: 'DTO for mapping Slack user groups to sub-teams',
      properties: {
        subGroupId: { description: 'Slack user group ID', type: 'string' },
        subTeamId: { description: 'Platform sub-team ID', type: 'string' }
      }
    },
    'UpdateSubGroupMappingDTO': {
      description: 'DTO for updating sub-group mappings',
      properties: {
        subGroupId: { description: 'Slack user group ID', type: 'string' },
        subTeamId: { description: 'Platform sub-team ID', type: 'string' }
      }
    },
    'CreateTriageRuleDto': {
      description: 'DTO for creating triage rules',
      properties: {
        name: { description: 'Rule name', type: 'string' },
        pattern: { description: 'Matching pattern', type: 'string' },
        priority: { description: 'Rule priority', type: 'number' },
        enabled: { description: 'Whether the rule is enabled', type: 'boolean' }
      }
    },
    'UpdateTriageRuleDto': {
      description: 'DTO for updating triage rules',
      properties: {
        name: { description: 'Rule name', type: 'string' },
        pattern: { description: 'Matching pattern', type: 'string' },
        priority: { description: 'Rule priority', type: 'number' },
        enabled: { description: 'Whether the rule is enabled', type: 'boolean' }
      }
    },
    'AddTeamDTO': {
      description: 'DTO for adding a team',
      properties: {
        teamId: { description: 'Platform team ID', type: 'string' },
        name: { description: 'Team name', type: 'string' }
      }
    },
    'MapTeamToChannelsDTO': {
      description: 'DTO for mapping teams to Slack channels',
      properties: {
        teamId: { description: 'Platform team ID', type: 'string' },
        channelIds: { description: 'Slack channel IDs', type: 'array', items: { type: 'string' } }
      }
    },
    'DisconnectTeamToChannelsDTO': {
      description: 'DTO for disconnecting teams from Slack channels',
      properties: {
        teamId: { description: 'Platform team ID', type: 'string' },
        channelIds: { description: 'Slack channel IDs', type: 'array', items: { type: 'string' } }
      }
    },
    'ConfigureTriageChannelDTO': {
      description: 'DTO for configuring triage channels',
      properties: {
        channelId: { description: 'Slack channel ID', type: 'string' },
        teamId: { description: 'Platform team ID', type: 'string' }
      }
    },
    'CreateTriageThreadDTO': {
      description: 'DTO for creating triage threads',
      properties: {
        channelId: { description: 'Slack channel ID', type: 'string' },
        messageText: { description: 'Message text', type: 'string' },
        userId: { description: 'User ID', type: 'string' }
      }
    },
    'PostMessageDTO': {
      description: 'DTO for posting messages to Slack',
      properties: {
        channelId: { description: 'Slack channel ID', type: 'string' },
        text: { description: 'Message text', type: 'string' },
        blocks: { description: 'Message blocks', type: 'array' }
      }
    },
    'UpdateMessageDTO': {
      description: 'DTO for updating Slack messages',
      properties: {
        channelId: { description: 'Slack channel ID', type: 'string' },
        ts: { description: 'Message timestamp', type: 'string' },
        text: { description: 'Updated message text', type: 'string' },
        blocks: { description: 'Updated message blocks', type: 'array' }
      }
    },
    'DeleteMessageDTO': {
      description: 'DTO for deleting Slack messages',
      properties: {
        channelId: { description: 'Slack channel ID', type: 'string' },
        ts: { description: 'Message timestamp', type: 'string' }
      }
    },
    'AddReactionDTO': {
      description: 'DTO for adding reactions to Slack messages',
      properties: {
        channelId: { description: 'Slack channel ID', type: 'string' },
        ts: { description: 'Message timestamp', type: 'string' },
        reaction: { description: 'Reaction emoji name', type: 'string' }
      }
    },
    'RemoveReactionDTO': {
      description: 'DTO for removing reactions from Slack messages',
      properties: {
        channelId: { description: 'Slack channel ID', type: 'string' },
        ts: { description: 'Message timestamp', type: 'string' },
        reaction: { description: 'Reaction emoji name', type: 'string' }
      }
    },
    'InviteUserToConversationDTO': {
      description: 'DTO for inviting users to Slack conversations',
      properties: {
        channelId: { description: 'Slack channel ID', type: 'string' },
        userId: { description: 'User ID to invite', type: 'string' }
      }
    },
    'KickUserFromConversationDTO': {
      description: 'DTO for removing users from Slack conversations',
      properties: {
        channelId: { description: 'Slack channel ID', type: 'string' },
        userId: { description: 'User ID to remove', type: 'string' }
      }
    },
    'JoinConversationDTO': {
      description: 'DTO for joining Slack conversations',
      properties: {
        channelId: { description: 'Slack channel ID', type: 'string' }
      }
    },
    'LeaveConversationDTO': {
      description: 'DTO for leaving Slack conversations',
      properties: {
        channelId: { description: 'Slack channel ID', type: 'string' }
      }
    }
  };

  // Apply schema enhancements
  for (const [schemaName, enhancedSchema] of Object.entries(schemaDescriptions)) {
    if (document.components?.schemas && schemaName in document.components.schemas) {
      document.components.schemas[schemaName] = {
        ...document.components.schemas[schemaName],
        ...(enhancedSchema as any)
      };
    }
  }

  // Add response examples and ensure request bodies are properly defined
  if (document.paths) {
    for (const [pathKey, pathObj] of Object.entries(document.paths)) {
      for (const [methodKey, methodObjRaw] of Object.entries(pathObj as any)) {
        // Skip if not a valid method object
        if (typeof methodObjRaw !== 'object' || methodObjRaw === null) continue;
        
        // Cast to our interface to fix TypeScript errors
        const methodObj = methodObjRaw as OpenAPIMethod;
        
        // Add operation ID if missing
        if (!methodObj.operationId) {
          // Generate an operationId based on the path and method
          const pathParts = pathKey.split('/').filter(p => p && !p.includes('{'));
          const lastPathPart = pathParts[pathParts.length - 1] || 'root';
          methodObj.operationId = `${methodKey}${lastPathPart.charAt(0).toUpperCase() + lastPathPart.slice(1)}`;
        }
        
        // Add description if missing
        if (!methodObj.description) {
          const action = {
            get: 'Retrieve',
            post: 'Create',
            put: 'Update',
            patch: 'Partially update',
            delete: 'Delete'
          }[methodKey] || 'Perform operation on';
          
          const resource = pathKey.split('/').filter(p => p && !p.includes('{')).pop() || 'resource';
          methodObj.description = `${action} ${resource}`;
        }
        
        // Add tags if missing
        if (!methodObj.tags || methodObj.tags.length === 0) {
          const pathSegments = pathKey.split('/');
          // Use the first non-empty segment after the initial slash as the tag
          const tag = pathSegments.filter(segment => segment && !segment.includes('{'))[0] || 'default';
          methodObj.tags = [tag.charAt(0).toUpperCase() + tag.slice(1)];
        }
        
        // Fix responses
        if (!methodObj.responses) {
          methodObj.responses = {};
        }
        
        // Ensure all common response codes are defined
        const responseTypes: Record<string, string> = {
          '200': 'Successful operation',
          '201': 'Created successfully',
          '400': 'Bad request - Invalid input',
          '401': 'Unauthorized - Authentication required',
          '403': 'Forbidden - Insufficient permissions',
          '404': 'Not found - Resource does not exist',
          '500': 'Internal server error'
        };
        
        // Add standard responses if they don't exist
        Object.entries(responseTypes).forEach(([statusCode, description]) => {
          if (methodKey === 'post' && statusCode === '201' && !methodObj.responses['201']) {
            methodObj.responses['201'] = { description };
          } else if (methodKey !== 'post' && statusCode === '200' && !methodObj.responses['200']) {
            methodObj.responses['200'] = { description };
          } else if (!methodObj.responses[statusCode] && statusCode !== '200' && statusCode !== '201') {
            // Add other standard responses
            methodObj.responses[statusCode] = { description };
          }
        });
        
        // Ensure response content is defined for 200 and 201
        if (methodObj.responses['200'] && !methodObj.responses['200'].content) {
          methodObj.responses['200'].description = methodObj.responses['200'].description || responseTypes['200'];
          methodObj.responses['200'].content = {
            'application/json': {
              schema: (methodObj.responses['200'] as any).schema || {
                type: 'object'
              }
            }
          };
        }
        
        if (methodObj.responses['201'] && !methodObj.responses['201'].content) {
          methodObj.responses['201'].description = methodObj.responses['201'].description || responseTypes['201'];
          methodObj.responses['201'].content = {
            'application/json': {
              schema: (methodObj.responses['201'] as any).schema || {
                type: 'object'
              }
            }
          };
        }
        
        // Add error response content
        ['400', '401', '403', '404', '500'].forEach(statusCode => {
          if (methodObj.responses[statusCode] && !methodObj.responses[statusCode].content) {
            methodObj.responses[statusCode].content = {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    statusCode: {
                      type: 'integer',
                      example: parseInt(statusCode)
                    },
                    message: {
                      type: 'string',
                      example: responseTypes[statusCode]
                    },
                    error: {
                      type: 'string',
                      example: statusCode === '400' ? 'Bad Request' : 
                              statusCode === '401' ? 'Unauthorized' :
                              statusCode === '403' ? 'Forbidden' :
                              statusCode === '404' ? 'Not Found' : 'Internal Server Error'
                    }
                  }
                }
              }
            };
          }
        });
        
        // Fix request bodies for methods that typically have them
        if (methodKey === 'post' || methodKey === 'put' || methodKey === 'patch') {
          if (!methodObj.requestBody) {
            methodObj.requestBody = {
              description: `Payload for ${methodObj.operationId || methodKey + ' operation'}`,
              required: true,
              content: {
                'application/json': {
                  schema: {
                    type: 'object'
                  }
                }
              }
            };
          } else if (methodObj.requestBody && !methodObj.requestBody.content) {
            methodObj.requestBody.content = {
              'application/json': {
                schema: {
                  type: 'object'
                }
              }
            };
          } else if (methodObj.requestBody && methodObj.requestBody.content && 
                    methodObj.requestBody.content['application/json'] && 
                    !methodObj.requestBody.content['application/json'].schema) {
            methodObj.requestBody.content['application/json'].schema = { type: 'object' };
          }
          
          // If requestBody exists but doesn't have a description, add one
          if (methodObj.requestBody && !methodObj.requestBody.description) {
            methodObj.requestBody.description = `Payload for ${methodObj.operationId || methodKey + ' operation'}`;
          }
          
          // If requestBody exists but required is not specified, set it to true
          if (methodObj.requestBody && methodObj.requestBody.required === undefined) {
            methodObj.requestBody.required = true;
          }
          
          // If we have a $ref in the schema, make sure the referenced schema has properties
          if (methodObj.requestBody && methodObj.requestBody.content && 
              methodObj.requestBody.content['application/json'] && 
              methodObj.requestBody.content['application/json'].schema && 
              methodObj.requestBody.content['application/json'].schema.$ref) {
            
            const refParts = methodObj.requestBody.content['application/json'].schema.$ref.split('/');
            const refSchemaName = refParts[refParts.length - 1];
            
            // Make sure the schema exists and has properties
            if (document.components?.schemas && refSchemaName in document.components.schemas) {
              const schema = document.components.schemas[refSchemaName] as any;
              
              // Add properties if missing
              if (!schema.properties || Object.keys(schema.properties).length === 0) {
                if (refSchemaName in globalSchemaDescriptions && globalSchemaDescriptions[refSchemaName].properties) {
                  schema.properties = globalSchemaDescriptions[refSchemaName].properties;
                }
              }
            }
          }
        }
        
        // Add detailed parameter descriptions
        if (Array.isArray(methodObj.parameters)) {
          methodObj.parameters.forEach((param: any) => {
            // If parameter doesn't have a schema, add one
            if (!param.schema) {
              if (param.in === 'query') {
                param.schema = { type: 'string' };
              } else if (param.in === 'path') {
                param.schema = { type: 'string' };
              } else if (param.in === 'header') {
                param.schema = { type: 'string' };
              }
            }
            
            // If parameter doesn't have a description, add one
            if (!param.description) {
              if (param.in === 'path') {
                param.description = `ID of the ${param.name}`;
              } else if (param.in === 'query') {
                if (param.name === 'page') {
                  param.description = 'Page number for pagination';
                } else if (param.name === 'limit' || param.name === 'size') {
                  param.description = 'Number of items per page';
                } else if (param.name === 'sort') {
                  param.description = 'Sort field and direction (e.g., name:asc)';
                } else if (param.name === 'filter' || param.name === 'search') {
                  param.description = 'Filter or search term';
                } else {
                  param.description = `Filter results by ${param.name}`;
                }
              } else if (param.in === 'header') {
                param.description = `${param.name} header value`;
              }
            }
            
            // If parameter doesn't have an example, add one
            if (!param.example && param.schema && param.schema.type) {
              if (param.schema.type === 'string') {
                if (param.name.toLowerCase().includes('id')) {
                  param.example = param.name.toLowerCase().includes('user') ? 'U12345678' :
                                 param.name.toLowerCase().includes('channel') ? 'C12345678' :
                                 param.name.toLowerCase().includes('team') ? 'T12345678' : 'id123';
                } else if (param.name === 'email') {
                  param.example = '<EMAIL>';
                } else if (param.name === 'name') {
                  param.example = 'Example Name';
                } else {
                  param.example = `example-${param.name}`;
                }
              } else if (param.schema.type === 'integer' || param.schema.type === 'number') {
                if (param.name === 'page') {
                  param.example = 1;
                } else if (param.name === 'limit' || param.name === 'size') {
                  param.example = 10;
                } else {
                  param.example = 42;
                }
              } else if (param.schema.type === 'boolean') {
                param.example = true;
              }
            }
          });
        }
      }
    }
  }
}

/**
 * Creates a Swagger output file from the document
 * @param document The Swagger document to save
 */
/**
 * Creates a Swagger output file from the document
 * @param document The Swagger document to save
 */
function createSwaggerOutputFile(document: OpenAPIObject): void {
  const outputDir = path.join(process.cwd(), 'swagger-output');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // Ensure all schemas have properties and examples
  if (document.components && document.components.schemas) {
    for (const [schemaName, schemaObj] of Object.entries(document.components.schemas)) {
      const schema = schemaObj as any;
      
      // Add properties if missing
      if (!schema.properties || Object.keys(schema.properties).length === 0) {
        // Add a default property if none exists
        schema.properties = schema.properties || {};
        
        if (Object.keys(schema.properties).length === 0) {
          // Check if we have a predefined schema for this
          if (schemaName in globalSchemaDescriptions && globalSchemaDescriptions[schemaName].properties) {
            schema.properties = globalSchemaDescriptions[schemaName].properties;
          } else {
            // Add security schemes with detailed descriptions
            document.components.securitySchemes = {
              ApiKeyAuth: {
                type: 'apiKey',
                in: 'header',
                name: 'X-API-KEY',
                description: 'API key for authentication. Must be included in the X-API-KEY header for all requests.',
              },
            };
            
            // Apply security globally
            document.security = [
              { ApiKeyAuth: [] },
            ];
            
            // Add common parameters that might be used across endpoints
            if (!document.components.parameters) {
              document.components.parameters = {};
            }
            
            document.components.parameters.TeamIdParam = {
              name: 'teamId',
              in: 'path',
              required: true,
              schema: {
                type: 'string',
              },
              description: 'The ID of the Slack team/workspace',
              example: 'T12345678'
            };
            
            document.components.parameters.ChannelIdParam = {
              name: 'channelId',
              in: 'path',
              required: true,
              schema: {
                type: 'string',
              },
              description: 'The ID of the Slack channel',
              example: 'C12345678'
            };
            
            document.components.parameters.UserIdParam = {
              name: 'userId',
              in: 'path',
              required: true,
              schema: {
                type: 'string',
              },
              description: 'The ID of the Slack user',
              example: 'U12345678'
            };
            
            document.components.parameters.PageParam = {
              name: 'page',
              in: 'query',
              required: false,
              schema: {
                type: 'integer',
                default: 1,
                minimum: 1
              },
              description: 'Page number for pagination',
              example: 1
            };
            
            document.components.parameters.LimitParam = {
              name: 'limit',
              in: 'query',
              required: false,
              schema: {
                type: 'integer',
                default: 10,
                minimum: 1,
                maximum: 100
              },
              description: 'Number of items per page',
              example: 10
            };
          }
        }
      }
      
      // Add description if missing
      if (!schema.description && schemaName in globalSchemaDescriptions && globalSchemaDescriptions[schemaName].description) {
        schema.description = globalSchemaDescriptions[schemaName].description;
      }
    }
  }
  
  fs.writeFileSync(
    path.join(outputDir, 'slack-openapi.yaml'),
    yaml.dump(document, {
      indent: 2,
      lineWidth: -1,
      noRefs: false, // Changed to false to maintain references
    })
  );
  console.log(`Swagger documentation generated at ${path.join(outputDir, 'slack-openapi.yaml')}`);
}
