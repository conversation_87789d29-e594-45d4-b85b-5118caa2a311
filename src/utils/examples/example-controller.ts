import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common';
import { ApiProperty, ApiTags } from '@nestjs/swagger';
import { ApiBodyWithExample, ApiDocumentation, ApiPathParams, ApiPaginationParams, ApiQueryParams } from '../decorators/api-documentation.decorator';

// Example DTOs
class CreateExampleDTO {
  @ApiProperty({ description: 'Name of the example', example: 'Example Name' })
  name: string;

  @ApiProperty({ description: 'Description of the example', example: 'This is an example description' })
  description: string;

  @ApiProperty({ description: 'Tags for the example', type: [String], example: ['tag1', 'tag2'] })
  tags: string[];
}

class ExampleResponseDTO {
  @ApiProperty({ description: 'Unique identifier', example: '123e4567-e89b-12d3-a456-426614174000' })
  id: string;

  @ApiProperty({ description: 'Name of the example', example: 'Example Name' })
  name: string;

  @ApiProperty({ description: 'Description of the example', example: 'This is an example description' })
  description: string;

  @ApiProperty({ description: 'Tags for the example', type: [String], example: ['tag1', 'tag2'] })
  tags: string[];

  @ApiProperty({ description: 'Creation timestamp', example: '2023-01-01T00:00:00.000Z' })
  createdAt: string;

  @ApiProperty({ description: 'Last update timestamp', example: '2023-01-02T00:00:00.000Z' })
  updatedAt: string;
}

/**
 * This is an example controller that demonstrates how to use the API documentation decorators
 * to generate comprehensive Swagger documentation for your API endpoints.
 */
@Controller('examples')
@ApiTags('Examples')
export class ExampleController {
  /**
   * Get all examples with pagination support
   */
  @Get()
  @ApiDocumentation({
    summary: 'Get all examples',
    description: 'Retrieves a paginated list of examples with optional filtering and sorting',
    responseType: ExampleResponseDTO,
    isArray: true,
    isPaginated: true,
  })
  @ApiPaginationParams()
  @ApiQueryParams([
    {
      name: 'search',
      description: 'Search term to filter examples by name or description',
      required: false,
      type: 'string',
      example: 'example',
    },
    {
      name: 'tags',
      description: 'Filter examples by tags',
      required: false,
      type: 'array',
      isArray: true,
      example: ['tag1', 'tag2'],
    },
  ])
  async getAllExamples(@Query() query: any): Promise<any> {
    // Implementation would go here
    return {
      data: [
        {
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Example 1',
          description: 'This is example 1',
          tags: ['tag1', 'tag2'],
          createdAt: '2023-01-01T00:00:00.000Z',
          updatedAt: '2023-01-01T00:00:00.000Z',
        },
      ],
      meta: {
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      },
    };
  }

  /**
   * Get a specific example by ID
   */
  @Get(':id')
  @ApiDocumentation({
    summary: 'Get example by ID',
    description: 'Retrieves a specific example by its unique identifier',
    responseType: ExampleResponseDTO,
  })
  @ApiPathParams([
    {
      name: 'id',
      description: 'Unique identifier of the example',
      required: true,
      example: '123e4567-e89b-12d3-a456-426614174000',
    },
  ])
  async getExampleById(@Param('id') id: string): Promise<any> {
    // Implementation would go here
    return {
      id: '123e4567-e89b-12d3-a456-426614174000',
      name: 'Example 1',
      description: 'This is example 1',
      tags: ['tag1', 'tag2'],
      createdAt: '2023-01-01T00:00:00.000Z',
      updatedAt: '2023-01-01T00:00:00.000Z',
    };
  }

  /**
   * Create a new example
   */
  @Post()
  @ApiDocumentation({
    summary: 'Create a new example',
    description: 'Creates a new example with the provided data',
    responseType: ExampleResponseDTO,
    status: 201,
  })
  @ApiBodyWithExample(CreateExampleDTO, 'Example creation payload')
  async createExample(@Body() createExampleDto: CreateExampleDTO): Promise<any> {
    // Implementation would go here
    return {
      id: '123e4567-e89b-12d3-a456-426614174000',
      ...createExampleDto,
      createdAt: '2023-01-01T00:00:00.000Z',
      updatedAt: '2023-01-01T00:00:00.000Z',
    };
  }
}
