# Swagger Documentation Guide

This guide explains how to properly document your APIs using Swagger in the Thena Slack application. Following these guidelines will ensure that all endpoints have comprehensive documentation, including request bodies, query parameters, path parameters, headers, and response types.

## Table of Contents

1. [Introduction](#introduction)
2. [Basic Controller Documentation](#basic-controller-documentation)
3. [DTO Documentation](#dto-documentation)
4. [Custom Decorators](#custom-decorators)
5. [Examples](#examples)
6. [Best Practices](#best-practices)

## Introduction

The Thena Slack application uses Swagger (OpenAPI) to document its APIs. We've enhanced the standard Swagger implementation with custom decorators and plugins to make it easier to create comprehensive API documentation.

The main enhancements include:
- Custom decorators for standardizing API documentation
- Base DTOs for common response patterns
- Automatic enhancement of Swagger schemas
- Consistent documentation of request bodies, query parameters, path parameters, headers, and response types

## Basic Controller Documentation

Every controller should be decorated with `@ApiTags` to categorize the endpoints:

```typescript
@Controller('members')
@ApiTags('Members')
export class MembersController {
  // ...
}
```

## DTO Documentation

All DTOs should use `@ApiProperty` decorators to document their properties:

```typescript
export class CreateUserDTO {
  @ApiProperty({
    description: 'The user\'s name',
    example: 'John Doe'
  })
  name: string;

  @ApiProperty({
    description: 'The user\'s email',
    example: '<EMAIL>'
  })
  email: string;
}
```

For paginated responses, extend the `PaginatedResponseDTO` from the base DTOs:

```typescript
export class UserResponseDTO {
  @ApiProperty({ example: 'U12345678' })
  id: string;

  @ApiProperty({ example: 'John Doe' })
  name: string;
}

// Use like this in your controller:
@ApiDocumentation({
  responseType: UserResponseDTO,
  isPaginated: true
})
```

## Custom Decorators

We've created several custom decorators to make API documentation easier:

### ApiDocumentation

This decorator standardizes the documentation for an endpoint:

```typescript
@ApiDocumentation({
  summary: 'Get all users',
  description: 'Retrieves a paginated list of users with optional filtering',
  responseType: UserResponseDTO,
  isArray: true,
  isPaginated: true,
})
```

### ApiBodyWithExample

This decorator documents the request body:

```typescript
@ApiBodyWithExample(CreateUserDTO, 'User creation payload')
```

### ApiQueryParams

This decorator documents query parameters:

```typescript
@ApiQueryParams([
  {
    name: 'search',
    description: 'Search term to filter users by name or email',
    required: false,
    type: 'string',
    example: 'john',
  },
])
```

### ApiPathParams

This decorator documents path parameters:

```typescript
@ApiPathParams([
  {
    name: 'id',
    description: 'User ID',
    required: true,
    example: 'U12345678',
  },
])
```

### ApiPaginationParams

This decorator adds standard pagination parameters:

```typescript
@ApiPaginationParams()
```

## Examples

Here's a complete example of a well-documented endpoint:

```typescript
@Get()
@ApiDocumentation({
  summary: 'Get all users',
  description: 'Retrieves a paginated list of users with optional filtering',
  responseType: UserResponseDTO,
  isArray: true,
  isPaginated: true,
})
@ApiPaginationParams()
@ApiQueryParams([
  {
    name: 'search',
    description: 'Search term to filter users by name or email',
    required: false,
    type: 'string',
    example: 'john',
  },
])
async getAllUsers(@Query() query: PaginationQueryDTO): Promise<PaginatedResponseDTO<UserResponseDTO>> {
  // Implementation
}
```

## Best Practices

1. **Always provide examples**: Examples help API consumers understand what data to expect.

2. **Use descriptive summaries and descriptions**: Make it clear what each endpoint does.

3. **Document all parameters**: Every query parameter, path parameter, and request body should be documented.

4. **Use appropriate response types**: Specify the correct response type for each endpoint.

5. **Use consistent naming**: Follow naming conventions for DTOs, controllers, and endpoints.

6. **Document error responses**: Include information about possible error responses.

7. **Use tags for organization**: Group related endpoints using tags.

8. **Keep documentation up to date**: Update the documentation when you change the API.

9. **Test the documentation**: Ensure that the Swagger UI correctly displays all information.

10. **Use the base DTOs**: Extend the base DTOs for common response patterns.

By following these guidelines, you'll ensure that all APIs in the Thena Slack application have comprehensive documentation that helps developers understand and use the APIs effectively.
