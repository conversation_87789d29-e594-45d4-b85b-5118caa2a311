// TODO: Add more details to the manifest
export const APP_MANIFEST = () => ({
  app_visibility: 'public',
  manifest: {
    app: {
      name: 'Slack',
      description: 'Thena Slack Integration',
      category: 'slack',
      icons: {
        small:
          'https://cdn4.iconfinder.com/data/icons/logos-and-brands/512/306_Slack_logo-512.png',
        large:
          'https://cdn4.iconfinder.com/data/icons/logos-and-brands/512/306_Slack_logo-512.png',
      },
      supported_locales: ['en-US'],
    },
    developer: {
      name: 'Thena',
      website: 'https://thena.ai',
      support_email: '<EMAIL>',
      privacy_policy_url: 'https://thena.ai/privacy',
      terms_url: 'https://thena.ai/terms',
      documentation_url: 'https://developers.thena.ai/docs',
    },
    integration: {
      entry_points: {
        main: 'https://app.thena.io/slack/integration',
        configuration: 'https://app.thena.io/slack/config',
        oauth_redirect: 'https://app.thena.io/slack/oauth/callback',
      },
      webhooks: {
        events: `${process.env.SLACK_APP_BASE_URL}/v1/platform/events`,
        installations: `${process.env.SLACK_APP_BASE_URL}/v1/platform/installations`,
      },
      interactivity: {
        request_url: 'https://api.thena.io/slack/interactive',
        message_menu_option_url: 'https://api.thena.io/slack/menu-options',
      },
    },
    configuration: {
      required_settings: [],
      optional_settings: [],
    },
    scopes: {
      required: {
        platform: [
          {
            scope: 'accounts:read',
            reason: 'Read company information',
            description: 'Access company data for synchronization',
          },
        ],
      },
    },
    events: {
      subscribe: [
        {
          event: 'account.created',
          reason: 'Sync new accounts to Hubspot',
          description:
            'Create companies in Hubspot when accounts are added to Thena',
        },
      ],
      publish: [
        {
          event: 'slack:member:join',
          reason: 'Notify when a member joins',
          schema: {
            type: 'object',
            properties: {
              member_id: {
                type: 'string',
              },
            },
          },
        },
      ],
    },
    activities: [
      {
        name: 'slack.postMessage',
        description: 'Sends a message on Slack',
        http_config: {
          endpoint_url: 'https://api.linear.app/graphql',
          httpVerb: 'POST',
          headers: {
            Authorization:
              'Bearer ${configuration.required_settings.linear_api_key}',
            'Content-Type': 'application/json',
          },
        },
        request_schema: {
          team_id: {
            type: 'string',
            description: 'Linear team ID',
          },
          channel_id: {
            type: 'string',
            description: 'Issue title',
          },
          message: {
            type: 'string',
            description: 'Issue description',
          },
          thread_ts: {
            type: 'number',
            description: 'Issue priority (0-4)',
          },
        },
        response_schema: {
          success: {
            type: 'boolean',
          },
        },
      },
    ],
    metadata: {
      is_privileged_app: true,
    },
  },
});
