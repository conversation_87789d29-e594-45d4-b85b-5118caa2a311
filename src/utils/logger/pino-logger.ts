import { Injectable } from '@nestjs/common';
import * as rTracer from 'cls-rtracer';
import pino, { DestinationStream, LoggerOptions } from 'pino';
import { ILogger } from './logger.interface';

const rTracerId = (): string => {
  const rid = rTracer.id() as string;
  return rid || '';
};

@Injectable()
export class PinoLoggerService implements ILogger {
  private readonly logger: pino.Logger;
  private readonly serviceTag: string;
  private readonly appTag: string;

  constructor(appTag: string, serviceTag: string) {
    this.appTag = appTag;
    this.serviceTag = serviceTag;

    const pinoConfig: LoggerOptions | DestinationStream = {
      level: 'debug',
      base: {
        app: this.appTag,
        service: this.serviceTag,
        mode: 'INGESTOR',
      },
      timestamp: pino.stdTimeFunctions.isoTime,
      formatters: {
        level: (label) => ({ level: label.toUpperCase() }),
      },
      mixin: () => ({ rid: rTracerId() }),
      messageKey: 'message',
    };

    this.logger = pino(pinoConfig);
  }

  private logWithContext(
    level: pino.Level,
    message: string,
    context?: string,
    extra?: Record<string, any>,
  ) {
    this.logger[level](
      {
        context,
        ...extra,
      },
      message,
    );
  }

  log(message: string, context?: string, extra?: Record<string, any>) {
    this.logWithContext('info', message, context, extra);
  }

  error(message: string, trace?: string, context?: string) {
    this.logWithContext(
      'error',
      message,
      context,
      trace ? { error: trace } : undefined,
    );
  }

  warn(message: string, context?: string) {
    this.logWithContext('warn', message, context);
  }

  debug(message: string, context?: string) {
    this.logWithContext('debug', message, context);
  }

  verbose(message: string, context?: string) {
    this.logWithContext('trace', message, context);
  }
}
