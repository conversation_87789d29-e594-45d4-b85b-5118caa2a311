import * as fs from 'node:fs';
import { Injectable } from '@nestjs/common';
import * as dotenv from 'dotenv';
import Joi from 'joi';

export enum ConfigKeys {
  // Core app
  NODE_ENV = 'NODE_ENV',
  APP_TAG = 'APP_TAG',
  SERVICE_TAG = 'SERVICE_TAG',
  MODE = 'MODE',

  // Slack Envs
  SLACK_APP_ID = 'SLACK_APP_ID',
  SLACK_CLIENT_ID = 'SLACK_CLIENT_ID',
  SLACK_CLIENT_SECRET = 'SLACK_CLIENT_SECRET',
  SLACK_SIGNING_SECRET = 'SLACK_SIGNING_SECRET',
  SLACK_STATE_SECRET = 'SLACK_STATE_SECRET',
  SLACK_APP_BASE_URL = 'SLACK_APP_BASE_URL',
  SLACK_EVENT_DEDUPLICATION_TTL_SECONDS = 'SLACK_EVENT_DEDUPLICATION_TTL_SECONDS',
  PLATFORM_EVENT_DEDUPLICATION_TTL_SECONDS = 'PLATFORM_EVENT_DEDUPLICATION_TTL_SECONDS',

  // Apps Platform API
  APPS_PLATFORM_API_URL = 'APPS_PLATFORM_API_URL',

  // Platform API
  PLATFORM_API_URL = 'PLATFORM_API_URL',
  PLATFORM_SALT = 'PLATFORM_SALT',

  // Annotator API
  ANNOTATOR_API_URL = 'ANNOTATOR_API_URL',

  // Thena Web
  THENA_WEB_URL = 'THENA_WEB_URL',

  // AWS
  AWS_ACCESS_KEY = 'AWS_ACCESS_KEY',
  AWS_SECRET_KEY = 'AWS_SECRET_KEY',
  AWS_REGION = 'AWS_REGION',

  // Queues
  AWS_SLACK_EVENTS_FIFO_QUEUE_URL = 'AWS_SLACK_EVENTS_FIFO_QUEUE_URL',

  // Database
  DB_HOST = 'DB_HOST',
  DB_PORT = 'DB_PORT',
  DB_USERNAME = 'DB_USERNAME',
  DB_PASSWORD = 'DB_PASSWORD',
  DB_NAME = 'DB_NAME',

  // Redis
  REDIS_HOST = 'REDIS_HOST',
  REDIS_PORT = 'REDIS_PORT',
  REDIS_PASSWORD = 'REDIS_PASSWORD',
  REDIS_USERNAME = 'REDIS_USERNAME',

  // Seeds
  DATABASE_AUTO_RUN_SEEDS = 'DATABASE_AUTO_RUN_SEEDS',

  // AI Providers
  GROK_API_KEY = 'GROK_API_KEY',
  CLAUDE_API_KEY = 'CLAUDE_API_KEY',
  OPENAI_API_KEY = 'OPENAI_API_KEY',

  // Vault
  VAULT_URL = 'VAULT_URL',
  VAULT_TOKEN = 'VAULT_TOKEN',
  CERT_PATH = 'CERT_PATH',

  // Sentry
  SENTRY_DSN = 'SENTRY_DSN',

  // Swagger
  GENERATE_SWAGGER = 'GENERATE_SWAGGER',
}

@Injectable()
export class ConfigService {
  private readonly envConfig: { [key: string]: string };

  constructor(filePath: string) {
    // Load the .env file
    const config = fs.existsSync(filePath)
      ? dotenv.parse(fs.readFileSync(filePath))
      : process.env;

    // Load the secrets from AWS Secrets Manager
    this.envConfig = this.validateEnv(config);
  }

  
  get(key: ConfigKeys) {
    if (!(key in this.envConfig)) {
      throw new Error(`Configuration key "${key}" does not exist`);
    }
    return String(this.envConfig[key]);
  }

  getBoolean(key: ConfigKeys, defaultValue: boolean = false): boolean {
    try {
      const value = String(this.get(key)).toLowerCase().trim();
      return value === 'true' || value === '1' || value === 'yes' || value === 'y';
    } catch (_error) {
      return defaultValue;
    }
  }

  get swaggerConfig() {
    return {  
      enabled: this.getBoolean(ConfigKeys.GENERATE_SWAGGER),
      title: this.get(ConfigKeys.SERVICE_TAG),
      description: this.get(ConfigKeys.APP_TAG),
      generateSwagger: this.get(ConfigKeys.GENERATE_SWAGGER),
    };
  }

  private validateEnv(config: { [key: string]: string }): {
    [key: string]: string;
  } {
    const envVarsSchema = Joi.object({
      // Core app
      [ConfigKeys.APP_TAG]: Joi.string().required(),
      [ConfigKeys.SERVICE_TAG]: Joi.string().required(),
      [ConfigKeys.NODE_ENV]: Joi.string()
        .valid('development', 'production', 'test', 'staging')
        .required(),
      [ConfigKeys.MODE]: Joi.string().valid('app', 'ingestor').default('app'),

      // Slack
      [ConfigKeys.SLACK_APP_ID]: Joi.string().required(),
      [ConfigKeys.SLACK_CLIENT_ID]: Joi.string().required(),
      [ConfigKeys.SLACK_CLIENT_SECRET]: Joi.string().required(),
      [ConfigKeys.SLACK_SIGNING_SECRET]: Joi.string().required(),
      [ConfigKeys.SLACK_STATE_SECRET]: Joi.string().required(),
      [ConfigKeys.SLACK_APP_BASE_URL]: Joi.string().required(),

      // Platform API
      [ConfigKeys.PLATFORM_API_URL]: Joi.string().required(),
      [ConfigKeys.PLATFORM_SALT]: Joi.string().required(),

      // Annotator API
      [ConfigKeys.ANNOTATOR_API_URL]: Joi.string().required(),

      // Thena Web
      [ConfigKeys.THENA_WEB_URL]: Joi.string().required(),

      // AWS
      [ConfigKeys.AWS_ACCESS_KEY]: Joi.string().required(),
      [ConfigKeys.AWS_SECRET_KEY]: Joi.string().required(),
      [ConfigKeys.AWS_REGION]: Joi.string().required(),
      [ConfigKeys.AWS_SLACK_EVENTS_FIFO_QUEUE_URL]: Joi.string().required(),

      // Database
      [ConfigKeys.DB_HOST]: Joi.string().required(),
      [ConfigKeys.DB_PORT]: Joi.number().required(),
      [ConfigKeys.DB_USERNAME]: Joi.string().required(),
      [ConfigKeys.DB_PASSWORD]: Joi.string().required(),
      [ConfigKeys.DB_NAME]: Joi.string().required(),

      // Redis
      [ConfigKeys.REDIS_HOST]: Joi.string().required(),
      [ConfigKeys.REDIS_PORT]: Joi.number().required(),
      [ConfigKeys.REDIS_PASSWORD]: Joi.string().required(),
      [ConfigKeys.REDIS_USERNAME]: Joi.string().required(),

      // Seeds
      [ConfigKeys.DATABASE_AUTO_RUN_SEEDS]: Joi.boolean().required(),

      // AI Providers
      [ConfigKeys.GROK_API_KEY]: Joi.string().required(),
      [ConfigKeys.CLAUDE_API_KEY]: Joi.string().required(),
      [ConfigKeys.OPENAI_API_KEY]: Joi.string().required(),

      // Sentry
      [ConfigKeys.SENTRY_DSN]: Joi.string().required(),

      // Vault
      [ConfigKeys.VAULT_URL]: Joi.string().required(),
      [ConfigKeys.VAULT_TOKEN]: Joi.string().required(),
      [ConfigKeys.CERT_PATH]: Joi.string().required(),

      // Swagger
      [ConfigKeys.GENERATE_SWAGGER]: Joi.boolean().default(false),

      // Event Deduplication
      [ConfigKeys.SLACK_EVENT_DEDUPLICATION_TTL_SECONDS]:
        Joi.number().default(86400),
      [ConfigKeys.PLATFORM_EVENT_DEDUPLICATION_TTL_SECONDS]:
        Joi.number().default(86400),
    }).unknown();


    // Validate the environment variables
    const { error, value: validatedEnvConfig } = envVarsSchema.validate(config);
    if (error) {
      throw new Error(`Config validation error: ${error.message}`);
    }

    return validatedEnvConfig;
  }
}
