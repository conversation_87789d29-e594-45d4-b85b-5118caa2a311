name: Gamma apps-slack-ingestor
on:
  push:
    branches:
      - master

env:
  ECR_REPOSITORY_NAME_GAMMA: apps-slack-ingestor
  ECS_CLUSTER_NAME_GAMMA: thena-backend-gamma
  SERVICE_NAME_GAMMA: apps-slack-ingestor
  AWS_REGION: us-east-1
  TASK_DEFINITION_NAME_GAMMA: apps-slack-ingestor

jobs:
  dockerize:
    runs-on: ubicloud-standard-2
    if: github.event_name == 'push' && github.ref == 'refs/heads/master'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Configure A<PERSON> credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_GAMMA }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY_GAMMA }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      
      - name: Build and push Docker image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: apps-slack-ingestor-${{ github.sha }} 
        run: |
          docker build -t $ECR_REGISTRY/${{ env.ECR_REPOSITORY_NAME_GAMMA }}:$IMAGE_TAG . --build-arg SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }}
          docker push $ECR_REGISTRY/${{ env.ECR_REPOSITORY_NAME_GAMMA }}:$IMAGE_TAG

  deploy:
    needs: dockerize
    runs-on: ubicloud-standard-2
    if: github.event_name == 'push' && github.ref == 'refs/heads/master'
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_GAMMA }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY_GAMMA }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      
      - name: Update ECS service
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: apps-slack-ingestor-${{ github.sha }}
        run: |
          FULL_IMAGE="${ECR_REGISTRY}/${ECR_REPOSITORY_NAME_GAMMA}:${IMAGE_TAG}"
          
          # Get current task definition
          TASK_DEFINITION=$(aws ecs describe-task-definition \
            --task-definition "${{ env.TASK_DEFINITION_NAME_GAMMA }}" \
            --include TAGS)
          
          # Create new task definition
          NEW_TASK_DEFINITION=$(echo $TASK_DEFINITION | \
            jq --arg IMAGE "$FULL_IMAGE" \
            '.taskDefinition | .containerDefinitions[0].image = $IMAGE | 
            del(.taskDefinitionArn) | del(.revision) | del(.status) | 
            del(.requiresAttributes) | del(.compatibilities) | 
            del(.registeredAt) | del(.registeredBy)')
          
          # Register new task definition
          NEW_TASK_INFO=$(aws ecs register-task-definition \
            --cli-input-json "$NEW_TASK_DEFINITION")
          
          NEW_REVISION=$(echo $NEW_TASK_INFO | jq '.taskDefinition.revision')
          
          # Update service
          aws ecs update-service \
            --cluster ${{ env.ECS_CLUSTER_NAME_GAMMA }} \
            --service ${{ env.SERVICE_NAME_GAMMA }} \
            --task-definition ${{ env.TASK_DEFINITION_NAME_GAMMA }}:${NEW_REVISION} \
            --force-new-deployment